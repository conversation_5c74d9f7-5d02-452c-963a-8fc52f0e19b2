[{"ground_truth_json": "{\"name\": \"Get Reactions Telegram\", \"nodes\": [{\"parameters\": {\"url\": \"={{ $json.api_check }}\", \"options\": {}}, \"name\": \"HTTP Request\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [1420, 140]}, {\"parameters\": {\"mode\": \"runOnceForEachItem\", \"jsCode\": \"function generateApiUrl(telegramLink) {\\n    const apiBase = \\\"http://54.169.0.232:3000/api/message?\\\";\\n    \\n    // S\\u1eed d\\u1ee5ng regex \\u0111\\u1ec3 l\\u1ea5y ph\\u1ea7n peer v\\u00e0 id cu\\u1ed1i c\\u00f9ng\\n    const match = telegramLink.match(/t\\\\.me\\\\/([\\\\w_]+)(?:\\\\/\\\\d+)*\\\\/(\\\\d+)/);\\n    if (!match) return \\\"Invalid Telegram link\\\";\\n\\n    const peer = match[1];\\n    const id = match[2];\\n\\n    // T\\u1ea1o URL API ho\\u00e0n ch\\u1ec9nh\\n    return `${apiBase}peer=${peer}&id=${id}`;\\n}\\n\\nreturn {\\n  api_check: generateApiUrl($json['Link message telegram'])\\n}\"}, \"name\": \"Code\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 2, \"position\": [1040, 260]}, {\"parameters\": {\"operation\": \"update\", \"documentId\": {\"__rl\": true, \"value\": \"1kdbl6o-T_HzCUfbnripELKrqKBXNpzOrzDIdsL3iRqo\", \"mode\": \"list\", \"cachedResultName\": \"Get Reactions Social\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1kdbl6o-T_HzCUfbnripELKrqKBXNpzOrzDIdsL3iRqo/edit?usp=drivesdk\"}, \"sheetName\": {\"__rl\": true, \"value\": 867241290, \"mode\": \"list\", \"cachedResultName\": \"Get reactions telegram\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1kdbl6o-T_HzCUfbnripELKrqKBXNpzOrzDIdsL3iRqo/edit#gid=867241290\"}, \"columns\": {\"mappingMode\": \"defineBelow\", \"value\": {\"Total ReactionEmoji\": \"={{ $json.totalReactions }}\", \"views\": \"={{ $json.views }}\", \"title\": \"={{ $json.title }}\", \"Link message telegram\": \"={{ $('Google Sheets Trigger').item.json['Link message telegram'] }}\", \"reactions\": \"={{ $json.reactions }}\"}, \"matchingColumns\": [\"Link message telegram\"], \"schema\": [{\"id\": \"Link message telegram\", \"displayName\": \"Link message telegram\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"Check\", \"displayName\": \"Check\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": true}, {\"id\": \"views\", \"displayName\": \"views\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"title\", \"displayName\": \"title\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"reactions\", \"displayName\": \"reactions\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"Total ReactionEmoji\", \"displayName\": \"Total ReactionEmoji\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"row_number\", \"displayName\": \"row_number\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"readOnly\": true, \"removed\": true}]}, \"options\": {}}, \"name\": \"Google Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.5, \"position\": [1820, 140]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"013084b1-35b1-4a9f-a612-b0e507bf7334\", \"name\": \"totalReactions\", \"value\": \"={{ $json.totalReactions }}\", \"type\": \"number\"}, {\"id\": \"8db7b2dc-9950-495a-a7c8-b832c5207748\", \"name\": \"reactions\", \"value\": \"={{ $json[\\\"reactions\\\"].map(r => r.count + \\\" \\\" + r.reaction.emoticon).join(\\\", \\\") }}\", \"type\": \"string\"}, {\"id\": \"5f53b01c-22b9-4c18-907c-a8293c313471\", \"name\": \"views\", \"value\": \"={{ $json.views }}\", \"type\": \"number\"}, {\"id\": \"3dd331e5-b937-411c-9cdd-82698fc9cb54\", \"name\": \"title\", \"value\": \"={{ $json.title ? $json.title:\\\"Kh\\u00f4ng c\\u00f3 title\\\" }}\", \"type\": \"string\"}]}, \"options\": {}}, \"name\": \"Edit Fields\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [1620, 140]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\", \"version\": 2}, \"conditions\": [{\"id\": \"7adb0f16-8200-4cd9-8dd2-a69a5ec1876b\", \"leftValue\": \"={{ $json.api_check }}\", \"rightValue\": \"Invalid Telegram link\", \"operator\": {\"type\": \"string\", \"operation\": \"notEquals\"}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"If\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2.2, \"position\": [1240, 260]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\", \"version\": 2}, \"conditions\": [{\"id\": \"ea25b040-16f6-4be6-a11c-a928ea45c75e\", \"leftValue\": \"={{ $json.Check }}\", \"rightValue\": \"Check\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\", \"name\": \"filter.operator.equals\"}}, {\"id\": \"ea762a87-567b-4d2c-a5de-88b49956c49f\", \"leftValue\": \"={{ $json['Link message telegram'] }}\", \"rightValue\": \"\", \"operator\": {\"type\": \"string\", \"operation\": \"exists\", \"singleValue\": true}}, {\"id\": \"31669864-b708-4d8c-ae47-cd4a8402e366\", \"leftValue\": \"={{ $json['Link message telegram'] }}\", \"rightValue\": \"https://t.me/\", \"operator\": {\"type\": \"string\", \"operation\": \"startsWith\"}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"If1\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2.2, \"position\": [880, 400]}, {\"parameters\": {\"pollTimes\": {\"item\": [{\"mode\": \"everyMinute\"}]}, \"documentId\": {\"__rl\": true, \"value\": \"1kdbl6o-T_HzCUfbnripELKrqKBXNpzOrzDIdsL3iRqo\", \"mode\": \"list\", \"cachedResultName\": \"Get Reactions Social\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1kdbl6o-T_HzCUfbnripELKrqKBXNpzOrzDIdsL3iRqo/edit?usp=drivesdk\"}, \"sheetName\": {\"__rl\": true, \"value\": 867241290, \"mode\": \"list\", \"cachedResultName\": \"Get reactions telegram\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1kdbl6o-T_HzCUfbnripELKrqKBXNpzOrzDIdsL3iRqo/edit#gid=867241290\"}, \"options\": {}}, \"name\": \"Google Sheets Trigger\", \"type\": \"n8n-nodes-base.googleSheetsTrigger\", \"typeVersion\": 1, \"position\": [680, 400]}], \"connections\": {\"Code\": {\"main\": [[{\"node\": \"If\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request\": {\"main\": [[{\"node\": \"Edit Fields\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields\": {\"main\": [[{\"node\": \"Google Sheets\", \"type\": \"main\", \"index\": 0}]]}, \"If\": {\"main\": [[{\"node\": \"HTTP Request\", \"type\": \"main\", \"index\": 0}]]}, \"If1\": {\"main\": [[{\"node\": \"Code\", \"type\": \"main\", \"index\": 0}]]}, \"Google Sheets Trigger\": {\"main\": [[{\"node\": \"If1\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Telegram Reaction Extractor\", \"nodes\": [{\"parameters\": {\"sheetName\": \"Telegram\", \"triggerOn\": \"rowUpdated\", \"columns\": [\"<PERSON>\", \"Reaction\", \"Count\"]}, \"name\": \"Google Sheets Trigger\", \"type\": \"n8n-nodes-base.googleSheetsTrigger\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"url\": \"https://api.telegram.org/bot<bot_token>/getReactions\", \"queryParameters\": {\"query\": {\"username\": \"={{ $json.username }}\"}}, \"headerParameters\": {\"Content-Type\": \"application/json\"}}, \"name\": \"HTTP Request\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 1, \"position\": [200, 0]}, {\"parameters\": {\"columns\": {\"Reaction\": \"={{ $json.reaction }}\", \"Count\": \"={{ $json.count }}\"}, \"sheetName\": \"Telegram\", \"operation\": \"update\"}, \"name\": \"Google Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 1, \"position\": [600, 0]}, {\"parameters\": {\"conditions\": {\"username\": {\"equals\": null}, \"reactions\": {\"equals\": null}}}, \"name\": \"Condition\", \"type\": \"n8n-nodes-base.condition\", \"typeVersion\": 1, \"position\": [400, 0]}, {\"parameters\": {\"columns\": {\"Reaction\": \"No Telegram link found\", \"Count\": \"0\"}}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [600, 200]}, {\"parameters\": {\"code\": \"const username = $json['Link'].match(/https:\\\\/\\\\/t\\\\.me\\\\/([^\\\\/]+)/)[1];\\n\\nif (username) {\\n  return { username: username };\\n} else {\\n  return null;\\n}\"}, \"name\": \"Code\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 1, \"position\": [100, 0]}, {\"parameters\": {\"code\": \"const reactions = $json['results']\\n  .map(result => ({\\n    reaction: result.reaction,\\n    count: result.count\\n  }));\\n\\nreturn { reactions: reactions };\"}, \"name\": \"Code1\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 1, \"position\": [300, 0]}], \"connections\": {\"Google Sheets Trigger\": {\"main\": [[{\"node\": \"Code\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request\": {\"main\": [[{\"node\": \"Code1\", \"type\": \"main\", \"index\": 0}]]}, \"Google Sheets\": {\"main\": [[{\"node\": \"Condition\", \"type\": \"main\", \"index\": 0}]]}, \"Condition\": {\"main\": [[{\"node\": \"Set\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Google Sheets\", \"type\": \"main\", \"index\": 0}]]}, \"Code\": {\"main\": [[{\"node\": \"HTTP Request\", \"type\": \"main\", \"index\": 0}]]}, \"Code1\": {\"main\": [[{\"node\": \"Condition\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"AI Intern Test - YouTube Analysis\", \"nodes\": [{\"parameters\": {\"path\": \"youtube-analysis\", \"method\": \"POST\", \"options\": {\"responseMode\": \"lastNode\"}, \"authentication\": \"none\"}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [250, 300]}, {\"parameters\": {\"functionCode\": \"const inputData = $json.body || $json;\\nconst url = inputData.url;\\n\\nif (!url) {\\n  throw new Error('URL not found in input. Please provide a valid YouTube channel URL.');\\n}\\n\\n// Handle both @username and channel ID formats\\nlet channelUsername = '';\\nlet channelId = '';\\n\\nif (url.includes('youtube.com/@')) {\\n  channelUsername = url.split('youtube.com/@')[1].split(/[/?#]/)[0];\\n  console.log('Username format detected:', channelUsername);\\n  return [{ json: { channelUsername, url, lookupNeeded: true } }];\\n} else if (url.includes('/channel/')) {\\n  channelId = url.split('/channel/')[1].split(/[/?#]/)[0];\\n  console.log('Channel ID format detected:', channelId);\\n  // Direct channelId - bypass lookup completely\\n  return [{ json: { channelId, url, lookupNeeded: false } }];\\n} else if (url.includes('/user/')) {\\n  channelUsername = url.split('/user/')[1].split(/[/?#]/)[0];\\n  return [{ json: { channelUsername, url, lookupNeeded: true } }];\\n} else if (url.includes('youtube.com/c/')) {\\n  channelUsername = url.split('youtube.com/c/')[1].split(/[/?#]/)[0];\\n  return [{ json: { channelUsername, url, lookupNeeded: true } }];\\n} else {\\n  // Assume it's a channel name or search query\\n  return [{ json: { lookupNeeded: true, query: url } }];\\n}\"}, \"name\": \"Parse Link\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"functionCode\": \"// Determine if we need to convert username to channel ID\\nif ($json.channelId) {\\n  // We already have the channel ID\\n  return [{ json: { channelId: $json.channelId, lookupNeeded: false } }];\\n} else if ($json.channelUsername || $json.query) {\\n  // We need to look up the channel ID from the username\\n  return [{ json: { channelUsername: $json.channelUsername || $json.query, lookupNeeded: true } }];\\n} else {\\n  throw new Error('Neither channel ID nor username found in the parsed URL');\\n}\"}, \"name\": \"Channel ID Logic\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [650, 300]}, {\"parameters\": {\"dataType\": \"boolean\", \"value1\": \"={{$json.lookupNeeded}}\", \"rules\": {\"rules\": [{\"value2\": true, \"operation\": \"equal\"}, {\"value2\": false, \"operation\": \"equal\"}]}}, \"name\": \"Channel Route Switch\", \"type\": \"n8n-nodes-base.switch\", \"typeVersion\": 1, \"position\": [750, 300]}, {\"parameters\": {\"url\": \"=https://www.googleapis.com/youtube/v3/search?part=snippet&type=channel&q={{$json.channelUsername || $json.query}}&key=AIzaSyBpJbjQVkDeXfcUCxwPUxWz1mt9Ss3Rxpk&maxResults=1\", \"method\": \"GET\", \"options\": {}}, \"name\": \"Lookup Channel ID\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [850, 200]}, {\"parameters\": {\"functionCode\": \"// Extract channel ID from search results\\nif ($json.items && $json.items.length > 0) {\\n  const channelId = $json.items[0].id.channelId;\\n  return [{ json: { channelId } }];\\n} else {\\n  throw new Error('Channel not found for the given username.');\\n}\"}, \"name\": \"Extract Channel ID\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [1050, 200]}, {\"parameters\": {\"url\": \"=https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics&id={{$json.channelId}}&key=AIzaSyBpJbjQVkDeXfcUCxwPUxWz1mt9Ss3Rxpk\", \"method\": \"GET\", \"options\": {}}, \"name\": \"Get Channel Info\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [1250, 300]}, {\"parameters\": {\"url\": \"=https://www.googleapis.com/youtube/v3/search?part=id&channelId={{$json.channelId}}&order=date&maxResults=10&type=video&key=AIzaSyBpJbjQVkDeXfcUCxwPUxWz1mt9Ss3Rxpk\", \"method\": \"GET\", \"options\": {}}, \"name\": \"Get Recent Videos\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [1250, 450]}, {\"parameters\": {\"functionCode\": \"// Extract video IDs\\nif (!$json.items || $json.items.length === 0) {\\n  return [{ json: { videoIds: '', error: 'No videos found' } }];\\n}\\n\\nconst ids = $json.items.map(i => i.id.videoId).join(',');\\nreturn [{ json: { videoIds: ids } }];\"}, \"name\": \"Collect IDs\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [1450, 450]}, {\"parameters\": {\"url\": \"=https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics&id={{$json.videoIds}}&key=AIzaSyBpJbjQVkDeXfcUCxwPUxWz1mt9Ss3Rxpk\", \"method\": \"GET\", \"options\": {}}, \"name\": \"Get Video Stats\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [1650, 450]}, {\"parameters\": {\"functionCode\": \"// Combine channel and video data\\nconst channelData = $items('Get Channel Info')[0].json;\\nconst videoData = $json;\\n\\nif (!channelData.items || channelData.items.length === 0) {\\n  throw new Error('No channel data found');\\n}\\n\\nconst ch = channelData.items[0];\\nconst vids = videoData.items || [];\\n\\n// Basic stats\\nconst stats = {\\n  name: ch.snippet.title,\\n  id: ch.id,\\n  publishedAt: ch.snippet.publishedAt,\\n  subscribers: parseInt(ch.statistics.subscriberCount) || 0,\\n  videoCount: parseInt(ch.statistics.videoCount) || 0,\\n  viewCount: parseInt(ch.statistics.viewCount) || 0\\n};\\n\\n// Content metrics\\nlet content = { avgLikes: 0, postingFreqDays: 0, engagement: 0 };\\n\\nif (vids.length > 0) {\\n  const totalLikes = vids.reduce((sum, v) => sum + (parseInt(v.statistics.likeCount) || 0), 0);\\n  const totalViews = vids.reduce((sum, v) => sum + (parseInt(v.statistics.viewCount) || 0), 0);\\n  const avgLikes = Math.round(totalLikes / vids.length);\\n  const avgViews = Math.round(totalViews / vids.length);\\n  \\n  // Calculate posting frequency\\n  const dates = vids.map(v => new Date(v.snippet.publishedAt));\\n  const oldestDate = new Date(Math.min(...dates));\\n  const freqDays = ((Date.now() - oldestDate) / (1000 * 60 * 60 * 24)) / vids.length;\\n  \\n  // Average engagement rate (likes per view)\\n  const engagement = (totalLikes / totalViews * 100).toFixed(2);\\n  \\n  content = {\\n    avgLikes,\\n    avgViews,\\n    postingFreqDays: freqDays.toFixed(1),\\n    engagement: engagement + '%'\\n  };\\n}\\n\\n// Extract common tags and topics\\nconst allTags = vids.flatMap(v => v.snippet.tags || []);\\nconst tagCount = {};\\nallTags.forEach(tag => {\\n  tagCount[tag] = (tagCount[tag] || 0) + 1;\\n});\\n\\n// Sort tags by frequency\\nconst topTags = Object.entries(tagCount)\\n  .sort((a, b) => b[1] - a[1])\\n  .slice(0, 5)\\n  .map(([tag]) => tag);\\n\\ncontent.topTags = topTags;\\n\\n// Final output\\nreturn [{ json: { stats, content, recentVideos: vids.slice(0, 5).map(v => ({ \\n  title: v.snippet.title,\\n  publishedAt: v.snippet.publishedAt,\\n  views: v.statistics.viewCount,\\n  likes: v.statistics.likeCount,\\n  url: https://www.youtube.com/watch?v=${v.id}\\n})) } }];\"}, \"name\": \"Compute Metrics\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [1850, 350]}, {\"parameters\": {\"url\": \"https://openrouter.ai/api/v1/chat/completions\", \"authentication\": \"predefinedCredentialType\", \"nodeCredentialType\": \"httpHeaderAuth\", \"method\": \"POST\", \"sendBody\": true, \"specifyBody\": \"json\", \"jsonBody\": {\"model\": \"openai/gpt-3.5-turbo\", \"messages\": [{\"role\": \"system\", \"content\": \"You are a YouTube analytics expert who provides insightful channel analysis reports.\"}, {\"role\": \"user\", \"content\": \"={{Generate a comprehensive YouTube channel analysis report using the following data: Basic stats: Subscribers: ${$json.stats.subscribers}, Views: ${$json.stats.viewCount}, Videos: ${$json.stats.videoCount}. Content metrics: AvgViews: ${$json.content.avgViews}, AvgLikes: ${$json.content.avgLikes}, Engagement: ${$json.content.engagement}. Provide a well-structured analysis that includes: 1. Channel overview & performance summary 2. Content strategy analysis 3. Audience engagement assessment 4. Competitive positioning within their niche 5. Growth trends and opportunities.}}\"}], \"temperature\": 0.3, \"max_tokens\": 1000}, \"options\": {\"redirect\": {\"followRedirects\": true}}}, \"name\": \"Analysis Report LLM\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [2050, 250]}, {\"parameters\": {\"url\": \"https://openrouter.ai/api/v1/chat/completions\", \"authentication\": \"predefinedCredentialType\", \"nodeCredentialType\": \"httpHeaderAuth\", \"method\": \"POST\", \"sendBody\": true, \"specifyBody\": \"json\", \"jsonBody\": {\"model\": \"openai/gpt-3.5-turbo\", \"messages\": [{\"role\": \"system\", \"content\": \"You are a YouTube content strategist who specializes in creating viral content ideas.\"}, {\"role\": \"user\", \"content\": \"Based on this YouTube channel's stats and metrics, suggest 5 specific viral content ideas that would work well for this creator.\\n\\nChannel stats: {{$json.stats}}\\nContent metrics: {{$json.content}}\\nRecent videos: {{$json.recentVideos}}\"}], \"temperature\": 0.7, \"max_tokens\": 1000}, \"options\": {\"redirect\": {\"followRedirects\": true}}}, \"name\": \"Prompt Suggestions LLM\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [2050, 400]}, {\"parameters\": {\"url\": \"https://openrouter.ai/api/v1/chat/completions\", \"authentication\": \"predefinedCredentialType\", \"nodeCredentialType\": \"httpHeaderAuth\", \"method\": \"POST\", \"sendBody\": true, \"specifyBody\": \"json\", \"jsonBody\": {\"model\": \"openai/gpt-3.5-turbo\", \"messages\": [{\"role\": \"system\", \"content\": \"You are an expert YouTube copywriter who can create engaging video titles and descriptions.\"}, {\"role\": \"user\", \"content\": \"Based on the recent videos from this YouTube channel, create 5 fresh title and description combinations that maintain the channel's style but could improve click-through rates.\\n\\nRecent videos: {{$json.recentVideos}}\\n\\nFor each recommendation, provide:\\n1. Original title\\n2. Improved title\\n3. Compelling description (2-3 sentences)\\n4. Tags to include\\n\\nMake sure the new titles and descriptions are optimized for search but still authentic to the channel's voice.\"}], \"temperature\": 0.7, \"max_tokens\": 1000}, \"options\": {\"redirect\": {\"followRedirects\": true}}}, \"name\": \"Copywriting LLM\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [2050, 550]}, {\"parameters\": {\"functionCode\": \"const analysis=$node['Analysis Report LLM'].json.choices[0].message.content;const ideas=$node['Prompt Suggestions LLM'].json.choices[0].message.content;const copy=$node['Copywriting LLM'].json.choices[0].message.content;const name=$node['Compute Metrics'].json.stats.name;return[{json:{channelName:name,channelAnalysis:analysis,contentIdeas:ideas,copywritingSuggestions:copy}}];\"}, \"name\": \"Compile Final Report\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [2250, 400]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Parse Link\", \"type\": \"main\", \"index\": 0}]]}, \"Parse Link\": {\"main\": [[{\"node\": \"Channel Route Switch\", \"type\": \"main\", \"index\": 0}]]}, \"Channel ID Logic\": {\"main\": [[{\"node\": \"Channel Route Switch\", \"type\": \"main\", \"index\": 0}]]}, \"Channel Route Switch\": {\"main\": [[{\"node\": \"Lookup Channel ID\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Get Channel Info\", \"type\": \"main\", \"index\": 0}]]}, \"Lookup Channel ID\": {\"main\": [[{\"node\": \"Extract Channel ID\", \"type\": \"main\", \"index\": 0}]]}, \"Extract Channel ID\": {\"main\": [[{\"node\": \"Get Channel Info\", \"type\": \"main\", \"index\": 0}]]}, \"Get Channel Info\": {\"main\": [[{\"node\": \"Get Recent Videos\", \"type\": \"main\", \"index\": 0}]]}, \"Get Recent Videos\": {\"main\": [[{\"node\": \"Collect IDs\", \"type\": \"main\", \"index\": 0}]]}, \"Collect IDs\": {\"main\": [[{\"node\": \"Get Video Stats\", \"type\": \"main\", \"index\": 0}]]}, \"Get Video Stats\": {\"main\": [[{\"node\": \"Compute Metrics\", \"type\": \"main\", \"index\": 0}]]}, \"Compute Metrics\": {\"main\": [[{\"node\": \"Analysis Report LLM\", \"type\": \"main\", \"index\": 0}]]}, \"Analysis Report LLM\": {\"main\": [[{\"node\": \"Prompt Suggestions LLM\", \"type\": \"main\", \"index\": 0}]]}, \"Prompt Suggestions LLM\": {\"main\": [[{\"node\": \"Copywriting LLM\", \"type\": \"main\", \"index\": 0}]]}, \"Copywriting LLM\": {\"main\": [[{\"node\": \"Compile Final Report\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"open_node_creator_for_connection\", \"nodes\": [{\"parameters\": {}, \"name\": \"When clicking \\u2018Test workflow\\u2019\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [740, 520]}, {\"parameters\": {}, \"name\": \"Token Splitter\", \"type\": \"@n8n/n8n-nodes-langchain.textSplitterTokenSplitter\", \"typeVersion\": 1, \"position\": [1180, 540]}, {\"parameters\": {}, \"name\": \"Memory\", \"type\": \"@n8n/n8n-nodes-langchain.memoryXata\", \"typeVersion\": 1.2, \"position\": [940, 540]}, {\"parameters\": {}, \"name\": \"Output Parser\", \"type\": \"@n8n/n8n-nodes-langchain.outputParserStructured\", \"typeVersion\": 1.1, \"position\": [1060, 540]}, {\"parameters\": {}, \"name\": \"Tool\", \"type\": \"@n8n/n8n-nodes-langchain.toolWikipedia\", \"typeVersion\": 1, \"position\": [1300, 540]}, {\"parameters\": {\"options\": {}}, \"name\": \"Embeddings\", \"type\": \"@n8n/n8n-nodes-langchain.embeddingsHuggingFaceInference\", \"typeVersion\": 1, \"position\": [1420, 540]}, {\"parameters\": {\"tableName\": {\"__rl\": true, \"mode\": \"list\", \"value\": \"\"}, \"options\": {}}, \"name\": \"Vector Store\", \"type\": \"@n8n/n8n-nodes-langchain.vectorStoreSupabase\", \"typeVersion\": 1, \"position\": [1540, 540]}], \"connections\": {}, \"active\": false}", "generated_json": "{\"name\": \"My workflow\", \"nodes\": [{\"parameters\": {}, \"name\": \"manualTrigger\", \"type\": \"manualTrigger\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {}, \"name\": \"tokenSplitter\", \"type\": \"tokenSplitter\", \"typeVersion\": 1, \"position\": [220, 0]}, {\"parameters\": {\"database\": \"my-database\", \"table\": \"my-table\"}, \"name\": \"Xata\", \"type\": \"xata\", \"typeVersion\": 1, \"position\": [440, 0]}, {\"parameters\": {}, \"name\": \"structuredOutputParser\", \"type\": \"structuredOutputParser\", \"typeVersion\": 1, \"position\": [660, 0]}, {\"parameters\": {}, \"name\": \"wikipedia\", \"type\": \"wikipedia\", \"typeVersion\": 1, \"position\": [880, 0]}, {\"parameters\": {\"modelName\": \"sentence-transformers/all-MiniLM-L6-v2\"}, \"name\": \"HuggingFaceEmbeddings\", \"type\": \"huggingFaceEmbeddings\", \"typeVersion\": 1, \"position\": [1100, 0]}, {\"parameters\": {\"supabaseUrl\": \"https://my-supabase-url.com\", \"supabaseApiKey\": \"my-supabase-api-key\"}, \"name\": \"SupabaseVectorStore\", \"type\": \"supabaseVectorStore\", \"typeVersion\": 1, \"position\": [1320, 0]}], \"connections\": {\"manualTrigger\": {\"main\": [[{\"node\": \"tokenSplitter\", \"type\": \"main\", \"index\": 0}]]}, \"tokenSplitter\": {\"main\": [[{\"node\": \"Xata\", \"type\": \"main\", \"index\": 0}]]}, \"Xata\": {\"main\": [[{\"node\": \"structuredOutputParser\", \"type\": \"main\", \"index\": 0}]]}, \"structuredOutputParser\": {\"main\": [[{\"node\": \"wikipedia\", \"type\": \"main\", \"index\": 0}]]}, \"wikipedia\": {\"main\": [[{\"node\": \"HuggingFaceEmbeddings\", \"type\": \"main\", \"index\": 0}]]}, \"HuggingFaceEmbeddings\": {\"main\": [[{\"node\": \"SupabaseVectorStore\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"rule\": {\"interval\": [{\"triggerAtHour\": 9}]}}, \"name\": \"Every day at 06:00\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1, \"position\": [960, 600]}, {\"parameters\": {\"query\": \"query ($filter: IssueFilter) {\\n  issues(filter: $filter, first: 100) {\\n    pageInfo {\\n      hasNextPage\\n      endCursor\\n    }\\n    nodes {\\n      id\\n      identifier\\n      url\\n      title\\n      priorityLabel\\n      createdAt\\n      completedAt\\n      state {\\n        type\\n        name\\n      }\\n      cycle {\\n        number\\n      }\\n      estimate\\n      labels { nodes { name } }\\n    }\\n  }\\n}\", \"endpoint\": \"https://api.linear.app/graphql\", \"variables\": \"={\\n  \\\"filter\\\": {\\n    \\\"team\\\": {\\n      \\\"name\\\":  {\\n        \\\"eq\\\": \\\"Adore\\\"\\n      }\\n    }\\n  }\\n}\", \"requestFormat\": \"json\", \"authentication\": \"headerAuth\"}, \"name\": \"Get all your team's tickets\", \"type\": \"n8n-nodes-base.graphql\", \"typeVersion\": 1, \"position\": [1200, 600]}, {\"parameters\": {\"color\": 7, \"width\": 256.14371825927645, \"height\": 100, \"content\": \"\\ud83d\\udc47\\ud83c\\udffd Set your team name here in the filter. \\n(Our team's name is Adore)\"}, \"name\": \"Sticky Note1\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [1200, 460]}, {\"parameters\": {\"options\": {}, \"conditions\": {\"options\": {\"leftValue\": \"\", \"caseSensitive\": true, \"typeValidation\": \"strict\"}, \"combinator\": \"and\", \"conditions\": [{\"id\": \"f5ab21aa-b2e0-4885-9278-6756c2c544f9\", \"operator\": {\"type\": \"boolean\", \"operation\": \"true\", \"singleValue\": true}, \"leftValue\": \"={{ $json.data.issues.pageInfo.hasNextPage }}\", \"rightValue\": 0}]}}, \"name\": \"if has next page\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2, \"position\": [1400, 780]}, {\"parameters\": {\"fields\": {\"values\": [{\"name\": \"after\", \"stringValue\": \"={{ $json.data.issues.pageInfo.endCursor }}\"}]}, \"include\": \"none\", \"options\": {}}, \"name\": \"Get end cursor\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1620, 920]}, {\"parameters\": {\"query\": \"=query ($filter: IssueFilter) {\\n  issues(filter: $filter, first: 100, after: \\\"{{ $json.after }}\\\") {\\n    nodes {\\n      id\\n      identifier\\n      url\\n      title\\n      priorityLabel\\n      createdAt\\n      completedAt\\n      state {\\n        type\\n        name\\n      }\\n      cycle {\\n        number\\n      }\\n      estimate\\n      labels { nodes { name } }\\n    }\\n    pageInfo {\\n      hasNextPage\\n      endCursor\\n    }\\n  }\\n}\", \"endpoint\": \"https://api.linear.app/graphql\", \"variables\": \"={\\n  \\\"filter\\\": {\\n    \\\"team\\\": {\\n      \\\"name\\\":  {\\n        \\\"eq\\\": \\\"Adore\\\"\\n      }\\n    }\\n  }\\n}\", \"requestFormat\": \"json\", \"authentication\": \"headerAuth\"}, \"name\": \"Get next page\", \"type\": \"n8n-nodes-base.graphql\", \"typeVersion\": 1, \"position\": [1880, 920]}, {\"parameters\": {\"color\": 7, \"width\": 256.14371825927645, \"height\": 100, \"content\": \"\\ud83d\\udc48\\ud83c\\udffd Set your team name here in the filter. \\n(Our team's name is Adore)\"}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [2060, 940]}, {\"parameters\": {\"options\": {}, \"fieldToSplitOut\": \"data.issues.nodes\"}, \"name\": \"Split out the tickets\", \"type\": \"n8n-nodes-base.splitOut\", \"typeVersion\": 1, \"position\": [2020, 600]}, {\"parameters\": {\"color\": 7, \"width\": 256.14371825927645, \"height\": 100, \"content\": \"\\ud83d\\udc47\\ud83c\\udffd Adjust any custom fields. Here we set labels and default estimate of 1\"}, \"name\": \"Sticky Note2\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [2240, 460]}, {\"parameters\": {\"fields\": {\"values\": [{\"name\": \"estimate\", \"type\": \"numberValue\", \"numberValue\": \"={{ $json.estimate ?? 1 }}\"}, {\"name\": \"labels\", \"stringValue\": \"={{ $json.labels.nodes.map((label) => label.name).toString() }}\"}]}, \"options\": {}}, \"name\": \"Set custom fields\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [2240, 600]}, {\"parameters\": {\"color\": 5, \"width\": 403.45318928152614, \"height\": 280.9004675550071, \"content\": \"### \\ud83d\\udc68\\u200d\\ud83c\\udfa4 Setup\\n1. Add Linear API header key\\n2. Add Google sheets creds\\n3. Update which teams to get tickets from in Graphql Nodes\\n4. Update which Google Sheets page to write all the tickets to. \\n **You only need to add one column, id. Google Sheets node in automatic mapping mode will handle adding the rest of the columns.**\\n5. Set any custom data on each ticket\\n6. Activate workflow \\ud83d\\ude80\"}, \"name\": \"Sticky Note3\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [740, 300]}, {\"parameters\": {\"color\": 7, \"width\": 256.14371825927645, \"height\": 100, \"content\": \"\\ud83d\\udc46\\ud83c\\udffd Update which Google sheet to write to\"}, \"name\": \"Sticky Note4\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [2700, 780]}, {\"parameters\": {\"columns\": {\"value\": {}, \"schema\": [{\"id\": \"id\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"id\", \"defaultMatch\": true, \"canBeUsedToMatch\": true}], \"mappingMode\": \"autoMapInputData\", \"matchingColumns\": [\"id\"]}, \"options\": {}, \"operation\": \"appendOrUpdate\", \"sheetName\": {\"__rl\": true, \"mode\": \"list\", \"value\": 2072772685, \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1pQxSIZ2dSoA8Fmr3B4EId9VOQXH1hVuOZgCHxcaKN7k/edit#gid=2072772685\", \"cachedResultName\": \"Sheet2\"}, \"documentId\": {\"__rl\": true, \"mode\": \"list\", \"value\": \"1pQxSIZ2dSoA8Fmr3B4EId9VOQXH1hVuOZgCHxcaKN7k\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1pQxSIZ2dSoA8Fmr3B4EId9VOQXH1hVuOZgCHxcaKN7k/edit?usp=drivesdk\", \"cachedResultName\": \"Adore tickets\"}}, \"name\": \"Write tickets to Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.2, \"position\": [2720, 600]}, {\"parameters\": {\"mode\": \"runOnceForEachItem\", \"jsCode\": \"function flattenObject(ob) {\\n    var toReturn = {};\\n\\n    for (var i in ob) {\\n        if (!ob.hasOwnProperty(i)) continue;\\n\\n        if ((typeof ob[i]) == 'object' && ob[i] !== null) {\\n            var flatObject = flattenObject(ob[i]);\\n            for (var x in flatObject) {\\n                if (!flatObject.hasOwnProperty(x)) continue;\\n\\n                toReturn[i + '.' + x] = flatObject[x];\\n            }\\n        } else {\\n            toReturn[i] = ob[i];\\n        }\\n    }\\n    return toReturn;\\n}\\n\\nreturn flattenObject($input.item.json);\"}, \"name\": \"Flatten object to have simple fields to filter by\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 2, \"position\": [2480, 600]}], \"connections\": {\"Get next page\": {\"main\": [[{\"node\": \"if has next page\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Split out the tickets\", \"type\": \"main\", \"index\": 0}]]}, \"Get end cursor\": {\"main\": [[{\"node\": \"Get next page\", \"type\": \"main\", \"index\": 0}]]}, \"if has next page\": {\"main\": [[{\"node\": \"Get end cursor\", \"type\": \"main\", \"index\": 0}]]}, \"Set custom fields\": {\"main\": [[{\"node\": \"Flatten object to have simple fields to filter by\", \"type\": \"main\", \"index\": 0}]]}, \"Every day at 06:00\": {\"main\": [[{\"node\": \"Get all your team's tickets\", \"type\": \"main\", \"index\": 0}]]}, \"Split out the tickets\": {\"main\": [[{\"node\": \"Set custom fields\", \"type\": \"main\", \"index\": 0}]]}, \"Get all your team's tickets\": {\"main\": [[{\"node\": \"Split out the tickets\", \"type\": \"main\", \"index\": 0}, {\"node\": \"if has next page\", \"type\": \"main\", \"index\": 0}]]}, \"Flatten object to have simple fields to filter by\": {\"main\": [[{\"node\": \"Write tickets to Sheets\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Get Tickets from Linear\", \"nodes\": [{\"parameters\": {\"teamId\": \"1234567890\", \"after\": \"={{ $json.pagination.next }}\"}, \"name\": \"Linear\", \"type\": \"n8n-nodes-base.linear\", \"typeVersion\": 1, \"position\": [100, 100]}, {\"parameters\": {\"fieldToSplit\": \"tickets\", \"batchSize\": 100}, \"name\": \"Loop Over Items\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 1, \"position\": [300, 100]}, {\"parameters\": {\"fields\": {\"custom1\": \"={{ $json.title }}\", \"custom2\": \"={{ $json.description }}\"}}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [500, 100]}, {\"parameters\": {\"sheetId\": \"1aBcD\", \"range\": \"A1\", \"mode\": \"append\"}, \"name\": \"Google Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 1, \"position\": [700, 100]}, {\"parameters\": {\"trigger\": \"schedule\", \"interval\": \"hour\"}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1, \"position\": [-100, 100]}], \"connections\": {\"Loop Over Items\": {\"main\": [[{\"node\": \"Linear\", \"type\": \"main\", \"index\": 0}]]}, \"Set\": {\"main\": [[{\"node\": \"Google Sheets\", \"type\": \"main\", \"index\": 0}]]}, \"Google Sheets\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Schedule Trigger\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Linear\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"topic\": \"={{ $('Creation Form').item.json.title }}\", \"authentication\": \"oAuth2\", \"additionalFields\": {\"password\": \"={{ Math.random().toString(36).slice(-4); }}\", \"startTime\": \"={{ new Date(new Date($('Creation Form').item.json.date_start).getTime() + ($('Creation Form').item.json.hour * 3600000) + ($('Creation Form').item.json.minute * 60000)).toISOString() }}\"}}, \"name\": \"Create Zoom meeting\", \"type\": \"n8n-nodes-base.zoom\", \"typeVersion\": 1, \"position\": [180, 480]}, {\"parameters\": {\"url\": \"https://api.stripe.com/v1/products\", \"method\": \"POST\", \"options\": {}, \"sendBody\": true, \"contentType\": \"form-urlencoded\", \"authentication\": \"predefinedCredentialType\", \"bodyParameters\": {\"parameters\": [{\"name\": \"name\", \"value\": \"={{ $('Creation Form').item.json.title }}\"}, {\"name\": \"default_price_data[unit_amount]\", \"value\": \"={{ $('Creation Form').item.json.price * 100 }}\"}, {\"name\": \"default_price_data[currency]\", \"value\": \"={{ $('Config').item.json.currency }}\"}]}, \"nodeCredentialType\": \"stripeApi\"}, \"name\": \"Create Stripe Product\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [400, 480]}, {\"parameters\": {\"options\": {}, \"assignments\": {\"assignments\": [{\"id\": \"038b54b7-9559-444e-8653-c5256a5b784e\", \"name\": \"currency\", \"type\": \"string\", \"value\": \"EUR\"}, {\"id\": \"64d1eeee-cabe-403b-a634-f3238f586f58\", \"name\": \"sheet_url\", \"type\": \"string\", \"value\": \"https://docs.google.com/spreadsheets/d/1ZliqqBNo6X0iM9yXBOiCG1e4Q7L7bQKMFmjvbSgUSnA/edit#gid=0\"}, {\"id\": \"997fe5a1-f601-458d-899c-673dff4acb04\", \"name\": \"teacher_email\", \"type\": \"string\", \"value\": \"<EMAIL>\"}]}}, \"name\": \"Config\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [-220, 640]}, {\"parameters\": {\"sendTo\": \"={{ $('Config').item.json.teacher_email }}\", \"message\": \"=<b>Congratulations, your event has been succesfully created \\ud83c\\udf89</b><br/><br/>\\n\\nTitle: {{ $('Creation Form').item.json.title }}<br/>\\nPrice:  {{ $('Creation Form').item.json.price }} {{ $('Config').item.json.currency }}<br/>\\nStart date: {{ $('Creation Form').item.json.date_start }}<br/><br/>\\n\\n<b>Payment link:</b><br/>\\n {{ $('Create payment link').item.json.url }}<br/>\\n<i>Start sharing this link to get subscriptions</i><br/><br/>\\n<b>Participant list:</b><br/>\\n{{ $('Config').item.json.sheet_url }}#gid={{ $('Create Stripe Product').item.json.created }}\\n<br/><br/>\\n<b>Zoom infos:</b><br/>\\nLink: {{ $('Create Zoom meeting').item.json.join_url }}<br/>\\nSession ID: {{ $('Create Zoom meeting').item.json.id }}<br/>\\nPassword: {{ $('Create Zoom meeting').item.json.password }}<br/> \", \"options\": {}, \"subject\": \"=\\ud83c\\udf89 {{ $('Creation Form').item.json.title }} has been created!\"}, \"name\": \"Send email to teacher\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [1040, 480]}, {\"parameters\": {\"title\": \"={{ $('Creation Form').item.json.date_start }} - {{ $('Creation Form').item.json.title }} - {{ $('Create Stripe Product').item.json.created }}\", \"options\": {\"index\": 0, \"sheetId\": \"={{ $('Create Stripe Product').item.json.created }}\"}, \"operation\": \"create\", \"documentId\": {\"__rl\": true, \"mode\": \"url\", \"value\": \"={{ $('Config').item.json.sheet_url }}\"}}, \"name\": \"Create participant list\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.3, \"position\": [840, 480]}, {\"parameters\": {\"columns\": {\"value\": {}, \"schema\": [{\"id\": \"city\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"city\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"email\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"email\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"name\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"name\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"country\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"country\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"postal_code\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"postal_code\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"amount\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"amount\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"currency\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"currency\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}], \"mappingMode\": \"autoMapInputData\", \"matchingColumns\": []}, \"options\": {}, \"operation\": \"append\", \"sheetName\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"={{ $('On payment').item.json.data.object.metadata.event_sheet_id }}\"}, \"documentId\": {\"__rl\": true, \"mode\": \"url\", \"value\": \"={{ $('Config').item.json.sheet_url }}\"}}, \"name\": \"Add participant to list\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.3, \"position\": [400, 800]}, {\"parameters\": {\"sendTo\": \"={{ $('On payment').item.json.data.object.customer_details.email }}\", \"message\": \"=Dear {{ $('On payment').item.json.data.object.customer_details.name }},<br/><br/>\\n\\nWe are very happy to announce that your subscription to our event <b>{{ $json.title }}</b> starting on <b>{{ $json.start }}</b> is now confirmed.<br/><br/>\\n\\nHere are the infos you will need to participate:<br/> \\nZoom link:  {{ $('On payment').item.json.data.object.metadata.zoom_link }}<br/>\\nZoom password:{{ $('On payment').item.json.data.object.metadata.zoom_password }}<br/>\\nZoom ID: {{ $('On payment').item.json.data.object.metadata.zoom_id }}<br/><br/> \\n\\nLooking forward to see you there!<br/>\\nKind regards<br/>\", \"options\": {\"appendAttribution\": false}, \"subject\": \"Than you for your subscription \\ud83d\\ude4f\"}, \"name\": \"Send confirmation to participant\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [620, 800]}, {\"parameters\": {\"sendTo\": \"={{ $('Config').item.json.teacher_email }}\", \"message\": \"=<b>A new participant registred for the event {{ $('Retrieve event infos').item.json.title }} ({{ $('Retrieve event infos').item.json.start }})!</b><br/><br/>\\n\\n<b>Name: {{ $('On payment').item.json.data.object.customer_details.name }}</b><br/>\\n<b>Email: {{ $('On payment').item.json.data.object.customer_details.email }}</b><br/><br/>\\n\\n<b>Participant list:</b><br/>\\n{{ $('Config').item.json.sheet_url }}#gid={{ $('On payment').item.json.data.object.metadata.event_sheet_id }} \", \"options\": {}, \"subject\": \"New participant registred \\u261d\\ufe0f\"}, \"name\": \"Notify teacher\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [840, 800]}, {\"parameters\": {\"url\": \"https://api.stripe.com/v1/payment_links\", \"method\": \"POST\", \"options\": {}, \"sendBody\": true, \"contentType\": \"form-urlencoded\", \"authentication\": \"predefinedCredentialType\", \"bodyParameters\": {\"parameters\": [{\"name\": \"line_items[0][price]\", \"value\": \"={{ $json.default_price }}\"}, {\"name\": \"line_items[0][quantity]\", \"value\": \"1\"}, {\"name\": \"metadata[event_sheet_id]\", \"value\": \"={{ $('Create Stripe Product').item.json.created }}\"}, {\"name\": \"metadata[zoom_link]\", \"value\": \"={{ $('Create Zoom meeting').item.json.join_url }}\"}, {\"name\": \"metadata[zoom_password]\", \"value\": \"={{ $('Create Zoom meeting').item.json.password }}\"}, {\"name\": \"metadata[zoom_id]\", \"value\": \"={{ $('Create Zoom meeting').item.json.id }}\"}, {\"name\": \"metadata[title]\", \"value\": \"={{ $('Creation Form').item.json.title }}\"}, {\"name\": \"metadata[start_time]\", \"value\": \"={{ $('Create Zoom meeting').item.json.start_time }}\"}, {\"name\": \"metadata[price]\", \"value\": \"={{ $('Creation Form').item.json.price }}\"}, {\"name\": \"metadata[currency]\", \"value\": \"={{ $('Config').item.json.currency }}\"}]}, \"nodeCredentialType\": \"stripeApi\"}, \"name\": \"Create payment link\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [620, 480]}, {\"parameters\": {\"options\": {}, \"assignments\": {\"assignments\": [{\"id\": \"dabd3bc2-ca92-4d99-a223-b0ad18945121\", \"name\": \"email\", \"type\": \"string\", \"value\": \"={{ $('On payment').item.json.data.object.customer_details.email }}\"}, {\"id\": \"d40709f6-ffcd-4055-a374-9044a9a5e3b2\", \"name\": \"name\", \"type\": \"string\", \"value\": \"={{ $('On payment').item.json.data.object.customer_details.name }}\"}]}}, \"name\": \"Format participant\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [180, 800]}, {\"parameters\": {\"options\": {}, \"assignments\": {\"assignments\": [{\"id\": \"a29943ba-b516-41a8-8f85-5bcee5eda0d1\", \"name\": \"title\", \"type\": \"string\", \"value\": \"={{ $('Creation Form').item.json.title }}\"}, {\"id\": \"bf642fde-c4c2-42b4-beed-ef65efdab55b\", \"name\": \"start\", \"type\": \"string\", \"value\": \"={{ $('Creation Form').item.json.date_start }}\"}, {\"id\": \"33f7a58e-624d-4ccc-bbea-ed3365cede20\", \"name\": \"price\", \"type\": \"number\", \"value\": \"={{ $('Creation Form').item.json.price }}\"}, {\"id\": \"c948f71e-3b12-4c6a-a1f9-ee9a511fe262\", \"name\": \"currency\", \"type\": \"string\", \"value\": \"={{ $('Config').item.json.currency }}\"}, {\"id\": \"887461ca-db0d-442e-8008-5fe6a6fbdd8f\", \"name\": \"zoom_link\", \"type\": \"string\", \"value\": \"={{ $('Create Zoom meeting').item.json.join_url }}\"}, {\"id\": \"4b2bd5e2-3bd5-443a-94a3-9ababfd9d881\", \"name\": \"zoom_id\", \"type\": \"string\", \"value\": \"={{ $('Create Zoom meeting').item.json.id }}\"}, {\"id\": \"a1cea8e2-9954-4143-b71f-5ea194a873dd\", \"name\": \"zoom_password\", \"type\": \"string\", \"value\": \"={{ $('Create Zoom meeting').item.json.password }}\"}, {\"id\": \"faa52bc6-dfbe-49e2-bc95-dae198a61293\", \"name\": \"payment_link\", \"type\": \"string\", \"value\": \"={{ $json.url }}\"}, {\"id\": \"d7f5f0f5-cc7b-436a-9ad1-0b8f410c62c6\", \"name\": \"payment_id\", \"type\": \"string\", \"value\": \"={{ $json.id }}\"}, {\"id\": \"020b22d0-f525-4120-9f8b-2fa33e88c2e1\", \"name\": \"event_sheet_id\", \"type\": \"string\", \"value\": \"={{ $json.metadata.event_sheet_id }}\"}]}}, \"name\": \"Format event\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [840, 280]}, {\"parameters\": {\"columns\": {\"value\": {}, \"schema\": [], \"mappingMode\": \"autoMapInputData\", \"matchingColumns\": []}, \"options\": {}, \"operation\": \"append\", \"sheetName\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"0\"}, \"documentId\": {\"__rl\": true, \"mode\": \"url\", \"value\": \"={{ $('Config').item.json.sheet_url }}\"}}, \"name\": \"Store event\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.3, \"position\": [1040, 280]}, {\"parameters\": {\"path\": \"1c6fe52c-48ab-4688-b5ae-7e24361aa602\", \"options\": {}, \"formTitle\": \"Create a new meeting\", \"formFields\": {\"values\": [{\"fieldLabel\": \"title\", \"requiredField\": true}, {\"fieldType\": \"number\", \"fieldLabel\": \"price\", \"requiredField\": true}, {\"fieldType\": \"date\", \"fieldLabel\": \"date_start\", \"requiredField\": true}, {\"fieldType\": \"number\", \"fieldLabel\": \"hour\"}, {\"fieldType\": \"number\", \"fieldLabel\": \"minute\"}]}, \"responseMode\": \"lastNode\", \"formDescription\": \"This automates the creation of a Zoom Meeting and a Stripe Payment page, streamlining your event setup process.\"}, \"name\": \"Creation Form\", \"type\": \"n8n-nodes-base.formTrigger\", \"typeVersion\": 2, \"position\": [-500, 480]}, {\"parameters\": {\"events\": [\"checkout.session.completed\"]}, \"name\": \"On payment\", \"type\": \"n8n-nodes-base.stripeTrigger\", \"typeVersion\": 1, \"position\": [-500, 780]}, {\"parameters\": {\"color\": 6, \"width\": 275.01592825011585, \"height\": 468.76027109756643, \"content\": \"# Setup\\n### 1/ Add Your credentials\\n[Zoom](https://docs.n8n.io/integrations/builtin/credentials/zoom/)\\n[Google](https://docs.n8n.io/integrations/builtin/credentials/google/)\\n[Stripe](https://docs.n8n.io/integrations/builtin/credentials/stripe/)\\n\\nNote: For Google, you need to add Gmail and Google Sheet.\\n\\n### 2/ Create a [new Google Sheet](https://sheets.new/).\\nKeep this sheet blank for now; it contains your meeting and participant information. Place it wherever it fits best in your organization.\\n\\n### 3/ And fill the config node\\n# \\ud83d\\udc47\"}, \"name\": \"Sticky Note1\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-220, 118]}, {\"parameters\": {\"color\": 6, \"width\": 372, \"height\": 200.14793114506386, \"content\": \"# Create a meeting \\ud83d\\udc49\\ud83c\\udffb\\n\\nYour journey to easy event management starts here.\\n\\nClick this node, copy the production URL, and keep it handy. It's your personal admin tool for quickly creating new meetings. Simple and efficient!\"}, \"name\": \"Sticky Note2\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-920, 500]}, {\"parameters\": {\"color\": 6, \"width\": 519.9859025074911, \"height\": 106.11515926602786, \"content\": \"# \\ud83d\\udd8b\\ufe0f Customize\\n### Feel free to adapt email contents to your needs.\"}, \"name\": \"Sticky Note3\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [620, 660]}, {\"parameters\": {\"options\": {\"looseTypeValidation\": true}, \"conditions\": {\"options\": {\"leftValue\": \"\", \"caseSensitive\": true, \"typeValidation\": \"loose\"}, \"combinator\": \"and\", \"conditions\": [{\"id\": \"40ddf809-1602-4120-ae7e-8be61437b50d\", \"operator\": {\"type\": \"boolean\", \"operation\": \"true\", \"singleValue\": true}, \"leftValue\": \"={{ $(\\\"Creation Form\\\").isExecuted }}\", \"rightValue\": \"\"}]}}, \"name\": \"if is creation flow\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2, \"position\": [-20, 640]}, {\"parameters\": {\"color\": 7, \"width\": 202.64787116404852, \"height\": 85.79488430601403, \"content\": \"### Crafted by the\\n## [\\ud83e\\udd77 n8n.ninja](https://n8n.ninja)\"}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [180, 340]}, {\"parameters\": {}, \"name\": \"the end\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [1040, 800]}], \"connections\": {\"Config\": {\"main\": [[{\"node\": \"if is creation flow\", \"type\": \"main\", \"index\": 0}]]}, \"On payment\": {\"main\": [[{\"node\": \"Config\", \"type\": \"main\", \"index\": 0}]]}, \"Format event\": {\"main\": [[{\"node\": \"Store event\", \"type\": \"main\", \"index\": 0}]]}, \"Creation Form\": {\"main\": [[{\"node\": \"Config\", \"type\": \"main\", \"index\": 0}]]}, \"Notify teacher\": {\"main\": [[{\"node\": \"the end\", \"type\": \"main\", \"index\": 0}]]}, \"Format participant\": {\"main\": [[{\"node\": \"Add participant to list\", \"type\": \"main\", \"index\": 0}]]}, \"Create Zoom meeting\": {\"main\": [[{\"node\": \"Create Stripe Product\", \"type\": \"main\", \"index\": 0}]]}, \"Create payment link\": {\"main\": [[{\"node\": \"Create participant list\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Format event\", \"type\": \"main\", \"index\": 0}]]}, \"if is creation flow\": {\"main\": [[{\"node\": \"Create Zoom meeting\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Format participant\", \"type\": \"main\", \"index\": 0}]]}, \"Create Stripe Product\": {\"main\": [[{\"node\": \"Create payment link\", \"type\": \"main\", \"index\": 0}]]}, \"Add participant to list\": {\"main\": [[{\"node\": \"Send confirmation to participant\", \"type\": \"main\", \"index\": 0}]]}, \"Create participant list\": {\"main\": [[{\"node\": \"Send email to teacher\", \"type\": \"main\", \"index\": 0}]]}, \"Send confirmation to participant\": {\"main\": [[{\"node\": \"Notify teacher\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"GetOnboardingFile Best Practices for Prompt Writing\", \"nodes\": [{\"parameters\": {\"path\": \"b6a48197-7776-45fb-9cec-88c976971b57\", \"responseMode\": \"responseNode\", \"options\": {}}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 2, \"position\": [0, 0]}, {\"parameters\": {\"operation\": \"get\", \"documentURL\": \"https://docs.google.com/document/d/1SWrpxi5jfVL79ftSA1u3PZL_wuzcfYFsA_5g02Kuet0/view\"}, \"name\": \"Google Docs\", \"type\": \"n8n-nodes-base.googleDocs\", \"typeVersion\": 2, \"position\": [220, 0]}, {\"parameters\": {\"respondWith\": \"text\", \"responseBody\": \"={{ $json.content }}\", \"options\": {}}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1.1, \"position\": [440, 0]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Google Docs\", \"type\": \"main\", \"index\": 0}]]}, \"Google Docs\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Get Google Docs content\", \"nodes\": [{\"parameters\": {\"httpMethod\": \"POST\", \"path\": \"webhook\"}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"operation\": \"get\", \"documentId\": \"1SWrpxi5jfVL79ftSA1u3PZL_wuzcfYFsA_5g02Kuet0\"}, \"name\": \"Google Docs\", \"type\": \"n8n-nodes-base.googleDocs\", \"typeVersion\": 1, \"position\": [650, 300]}, {\"parameters\": {\"respondWith\": \"text\", \"responseBody\": \"={{$json.content}}\"}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1, \"position\": [850, 300]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Google Docs\", \"type\": \"main\", \"index\": 0}]]}, \"Google Docs\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Node IO filter\", \"nodes\": [{\"parameters\": {}, \"name\": \"When clicking \\u2018Test workflow\\u2019\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [840, 180]}, {\"parameters\": {\"options\": {}}, \"name\": \"Edit Fields\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1080, 180]}, {\"parameters\": {\"conditions\": {\"string\": [{\"value1\": \"={{ $json.profile.name }}\", \"operation\": \"contains\", \"value2\": \"an\"}]}}, \"name\": \"IF\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 1, \"position\": [1300, 180]}, {\"parameters\": {\"options\": {}}, \"name\": \"True\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1560, 60]}, {\"parameters\": {\"options\": {}}, \"name\": \"False\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1560, 240]}], \"connections\": {\"When clicking \\u2018Test workflow\\u2019\": {\"main\": [[{\"node\": \"Edit Fields\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields\": {\"main\": [[{\"node\": \"IF\", \"type\": \"main\", \"index\": 0}]]}, \"IF\": {\"main\": [[{\"node\": \"True\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"False\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Filter data based on profile name\", \"nodes\": [{\"parameters\": {}, \"name\": \"manualTrigger\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [100, 100]}, {\"parameters\": {\"fields\": {\"profileName\": \"an\"}}, \"name\": \"editFields\", \"type\": \"n8n-nodes-base.editFields\", \"typeVersion\": 1, \"position\": [300, 100]}, {\"parameters\": {\"conditions\": {\"contains\": [{\"value1\": \"an\"}]}}, \"name\": \"IF\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 1, \"position\": [500, 100]}, {\"parameters\": {\"fields\": {\"profileName\": \"={{ $json.profileName }}\"}}, \"name\": \"True\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [700, 100]}, {\"parameters\": {\"fields\": {\"profileName\": \"={{ $json.profileName }}\"}}, \"name\": \"False\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [700, 300]}], \"connections\": {\"manualTrigger\": {\"main\": [[{\"node\": \"editFields\", \"type\": \"main\", \"index\": 0}]]}, \"editFields\": {\"main\": [[{\"node\": \"IF\", \"type\": \"main\", \"index\": 0}]]}, \"IF\": {\"main\": [[{\"node\": \"True\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"False\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"url\": \"https://api.github.com/notifications\", \"options\": {}, \"authentication\": \"basicAuth\", \"queryParametersUi\": {\"parameter\": [{\"name\": \"since\", \"value\": \"={{$node[\\\"@Get Date 1 min ago\\\"].json[\\\"since\\\"]}}\"}]}, \"headerParametersUi\": {\"parameter\": [{\"name\": \"User-Agent\", \"value\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.135 Safari/537.36 Edge/12.246\"}]}}, \"name\": \"@Get Issue\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 1, \"position\": [1050, 590]}, {\"parameters\": {\"triggerTimes\": {\"item\": [{\"mode\": \"everyX\", \"unit\": \"minutes\", \"value\": 1}]}}, \"name\": \"Cron\", \"type\": \"n8n-nodes-base.cron\", \"typeVersion\": 1, \"position\": [710, 590]}, {\"parameters\": {\"text\": \"=Notifications In last minutes: <@userIdForTagging>\\n{{$node[\\\"Function\\\"].json[\\\"reportMessage\\\"]}}\"}, \"name\": \"Discord\", \"type\": \"n8n-nodes-base.discord\", \"typeVersion\": 1, \"position\": [1610, 580]}, {\"parameters\": {\"functionCode\": \"const newItems = [];\\n\\nfor (const item of items[0].json) {\\n     newItems.push(`- [${item.reason}] => ${item.subject.title} @ ${item.subject.url.replace('api.','').replace('/repos','')}`);\\n  }\\n\\nreturn [{json: {reportMessage: `${newItems.join('\\\\r\\\\n')}`, hasNotifications: items[0].json.length > 0}}];\\n\"}, \"name\": \"Function\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [1230, 590]}, {\"parameters\": {\"conditions\": {\"boolean\": [{\"value1\": \"={{$node[\\\"Function\\\"].json[\\\"hasNotifications\\\"]}}\", \"value2\": true}]}}, \"name\": \"IF\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 1, \"position\": [1400, 590]}, {\"parameters\": {\"functionCode\": \"const date = new Date(new Date().setMinutes(new Date().getMinutes() - (1))).toISOString()\\nreturn [{json: {since: date}}];\"}, \"name\": \"@Get Date 1 min ago\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [860, 590]}], \"connections\": {\"IF\": {\"main\": [[{\"node\": \"Discord\", \"type\": \"main\", \"index\": 0}]]}, \"Cron\": {\"main\": [[{\"node\": \"@Get Date 1 min ago\", \"type\": \"main\", \"index\": 0}]]}, \"Function\": {\"main\": [[{\"node\": \"IF\", \"type\": \"main\", \"index\": 0}]]}, \"@Get Issue\": {\"main\": [[{\"node\": \"Function\", \"type\": \"main\", \"index\": 0}]]}, \"@Get Date 1 min ago\": {\"main\": [[{\"node\": \"@Get Issue\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"GitHub Notifications\", \"nodes\": [{\"parameters\": {\"resource\": \"notification\", \"operation\": \"getAll\"}, \"name\": \"GitHub Notifications\", \"type\": \"n8n-nodes-base.github\", \"typeVersion\": 1, \"position\": [100, 100]}, {\"parameters\": {\"webhookUrl\": \"https://discord.com/api/webhooks/...\", \"message\": \"=You have {{ $items.length }} new GitHub notifications:\\n\\n{{ $items.map(item => item.title).join(\\\"\\\\n\\\") }}\"}, \"name\": \"Send Message\", \"type\": \"n8n-nodes-base.discord\", \"typeVersion\": 1, \"position\": [600, 200]}, {\"parameters\": {\"conditions\": {\"boolean\": [{\"value1\": \"={{ $items.length > 0 }}\"}]}}, \"name\": \"Check Notifications\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 1, \"position\": [400, 200]}, {\"parameters\": {\"conditions\": {\"timestamp\": [{\"value1\": \"={{ $json.updated_at }}\", \"value2\": \"={{ $node[\\\"Calculate Time\\\"].json[\\\"lastMinute\\\"] }}\"}]}}, \"name\": \"Filter Notifications\", \"type\": \"n8n-nodes-base.filter\", \"typeVersion\": 1, \"position\": [300, 100]}, {\"parameters\": {\"values\": {\"lastMinute\": \"={{ new Date(new Date().getTime() - 60000) }}\"}}, \"name\": \"Calculate Time\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [200, 100]}, {\"parameters\": {\"trigger\": \"cron\", \"expression\": \"* * * * *\"}, \"name\": \"Cron\", \"type\": \"n8n-nodes-base.cron\", \"typeVersion\": 1, \"position\": [0, 100]}], \"connections\": {\"Cron\": {\"main\": [[{\"node\": \"GitHub Notifications\", \"type\": \"main\", \"index\": 0}]]}, \"GitHub Notifications\": {\"main\": [[{\"node\": \"Calculate Time\", \"type\": \"main\", \"index\": 0}]]}, \"Calculate Time\": {\"main\": [[{\"node\": \"Filter Notifications\", \"type\": \"main\", \"index\": 0}]]}, \"Filter Notifications\": {\"main\": [[{\"node\": \"Check Notifications\", \"type\": \"main\", \"index\": 0}]]}, \"Check Notifications\": {\"main\": [[{\"node\": \"Send Message\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Main workflow loop backup\", \"nodes\": [{\"parameters\": {}, \"name\": \"On clicking 'execute'\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [1640, 580]}, {\"parameters\": {\"filters\": {}, \"requestOptions\": {}}, \"name\": \"n8n\", \"type\": \"n8n-nodes-base.n8n\", \"typeVersion\": 1, \"position\": [2040, 680]}, {\"parameters\": {\"options\": {}}, \"name\": \"Loop Over Items\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [2240, 680]}, {\"parameters\": {\"rule\": {\"interval\": [{\"triggerAtHour\": 1, \"triggerAtMinute\": 33}]}}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.2, \"position\": [1620, 800]}, {\"parameters\": {\"content\": \"## Main workflow loop\", \"height\": 416.1856906618075, \"width\": 1272.6408145680155, \"color\": 7}, \"name\": \"Sticky Note2\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [1620, 500]}, {\"parameters\": {\"workflowId\": \"=rkExj7m8P27lT2xs\", \"options\": {}}, \"name\": \"Execute Workflow\", \"type\": \"n8n-nodes-base.executeWorkflow\", \"typeVersion\": 1, \"position\": [2460, 720]}, {\"parameters\": {\"sendTo\": \"<EMAIL>\", \"subject\": \"=Backup Started {{ $execution.id }}\", \"emailType\": \"text\", \"message\": \"=:information_source:  Starting Workflow Backup [{{ $execution.id }}]\", \"options\": {}}, \"name\": \"Gmail\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [1860, 220]}, {\"parameters\": {\"sendTo\": \"<EMAIL>\", \"subject\": \"=Backup Started {{ $execution.id }}\", \"emailType\": \"text\", \"message\": \"=\\u2705 Backup has completed - {{ $('n8n').all().length }} workflows have been processed.\", \"options\": {}}, \"name\": \"Gmail1\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [2460, 360]}, {\"parameters\": {\"sendTo\": \"<EMAIL>\", \"subject\": \"=Backup Started {{ $execution.id }}\", \"emailType\": \"text\", \"message\": \"=:x: Failed to backup {{ $('Loop Over Items').item.json.id }}\", \"options\": {}}, \"name\": \"Gmail2\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [2660, 1020]}, {\"parameters\": {\"chatId\": \"489359810\", \"text\": \"=:information_source:  Starting Workflow Backup [{{ $execution.id }}]\", \"additionalFields\": {}}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [1860, 680]}, {\"parameters\": {\"chatId\": \"489359810\", \"text\": \"=Backup n8n.psyii.od.ua/workflow Done {{ $execution.id }}\", \"additionalFields\": {}}, \"name\": \"Telegram1\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [2460, 520]}, {\"parameters\": {\"chatId\": \"489359810\", \"text\": \"=:x: Failed to backup {{ $('Loop Over Items').item.json.id }}\", \"additionalFields\": {}}, \"name\": \"Telegram2\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [2720, 780]}], \"connections\": {\"On clicking 'execute'\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}, \"n8n\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Loop Over Items\": {\"main\": [[{\"node\": \"Telegram1\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Execute Workflow\", \"type\": \"main\", \"index\": 0}]]}, \"Schedule Trigger\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}, \"Execute Workflow\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Telegram2\", \"type\": \"main\", \"index\": 0}]]}, \"Gmail\": {\"main\": [[]]}, \"Gmail2\": {\"main\": [[]]}, \"Telegram\": {\"main\": [[{\"node\": \"n8n\", \"type\": \"main\", \"index\": 0}]]}, \"Telegram2\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Backup Other Workflows\", \"nodes\": [{\"parameters\": {\"trigger\": \"schedule\", \"triggerInterval\": [{\"triggerAtHour\": 10}]}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1, \"position\": [100, 100]}, {\"parameters\": {\"operation\": \"getAll\", \"returnAll\": true}, \"name\": \"n8n\", \"type\": \"n8n-nodes-base.n8n\", \"typeVersion\": 1, \"position\": [300, 100]}, {\"parameters\": {\"loopOverItems\": true}, \"name\": \"Loop Over Items\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 1, \"position\": [500, 100]}, {\"parameters\": {\"operation\": \"executeWorkflow\", \"workflowId\": \"={{$node[\\\"n8n\\\"].data[\\\"id\\\"]}}\", \"workflowName\": \"={{$node[\\\"n8n\\\"].data[\\\"name\\\"]}}\"}, \"name\": \"n8n1\", \"type\": \"n8n-nodes-base.n8n\", \"typeVersion\": 1, \"position\": [700, 100]}, {\"parameters\": {\"chatId\": \"1234567890\", \"message\": \"Backup executed for workflow: {{$node[\\\"n8n1\\\"].data[\\\"executionId\\\"]}}\"}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1, \"position\": [900, 100]}, {\"parameters\": {\"to\": \"<EMAIL>\", \"subject\": \"Backup executed\", \"message\": \"Backup executed for workflow: {{$node[\\\"n8n1\\\"].data[\\\"executionId\\\"]}}\"}, \"name\": \"Gmail\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 1, \"position\": [900, 200]}], \"connections\": {\"Schedule Trigger\": {\"main\": [[{\"node\": \"n8n\", \"type\": \"main\", \"index\": 0}]]}, \"n8n\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Loop Over Items\": {\"main\": [[{\"node\": \"n8n1\", \"type\": \"main\", \"index\": 0}]]}, \"n8n1\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Gmail\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"[API OFICIAL] - Disparo em massa 10/10/2024\", \"nodes\": [{\"parameters\": {\"operation\": \"limit\", \"keep\": \"lastItems\"}, \"name\": \"Item Lists1\", \"type\": \"n8n-nodes-base.itemLists\", \"typeVersion\": 3, \"position\": [-340, 400]}, {\"parameters\": {\"documentId\": {\"__rl\": true, \"value\": \"1LphZOAErogV5YOb7oOMwAYVuMJD1Z9rtZOoHVVxAmVQ\", \"mode\": \"id\"}, \"sheetName\": {\"__rl\": true, \"value\": 441325879, \"mode\": \"list\", \"cachedResultName\": \"10/10/2024\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1LphZOAErogV5YOb7oOMwAYVuMJD1Z9rtZOoHVVxAmVQ/edit#gid=441325879\"}, \"options\": {}}, \"name\": \"le_planilha1\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 3, \"position\": [-820, 580]}, {\"parameters\": {\"options\": {}}, \"name\": \"separa_em_lotes1\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [-580, 580]}, {\"parameters\": {}, \"name\": \"N\\u00e3o faz nada1\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [260, 600]}, {\"parameters\": {\"jsCode\": \"const telefoneOriginal = $node[\\\"separa_em_lotes1\\\"].json[\\\"telefone\\\"].toString();\\n\\n// Removendo caracteres n\\u00e3o num\\u00e9ricos\\nconst telefoneLimpo = telefoneOriginal.replace(/\\\\D/g, '');\\n\\n// Define o DDI padr\\u00e3o\\nconst ddiPadrao = '55';\\n\\n// Extrai o DDD e o n\\u00famero, considerando que o telefone limpo pode j\\u00e1 vir com o DDI\\nlet ddd, numero;\\nif (telefoneLimpo.startsWith(ddiPadrao)) {\\n    // Se o n\\u00famero j\\u00e1 come\\u00e7a com o DDI padr\\u00e3o, extrai sem o DDI\\n    ddd = telefoneLimpo.slice(2, 4);\\n    numero = telefoneLimpo.slice(4);\\n} else {\\n    // Se o n\\u00famero n\\u00e3o cont\\u00e9m o DDI, extrai o DDD e o n\\u00famero como est\\u00e3o\\n    ddd = telefoneLimpo.slice(0, 2);\\n    numero = telefoneLimpo.slice(2);\\n}\\n\\n// Monta o n\\u00famero de telefone final, adicionando o DDI apenas se n\\u00e3o estava presente\\nconst telefoneFinal = telefoneLimpo.startsWith(ddiPadrao) ? ddiPadrao + ddd + numero : ddiPadrao + telefoneLimpo;\\n\\nreturn { telefonefinal: telefoneFinal };\\n\"}, \"name\": \"Code1\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 2, \"position\": [-340, 600]}, {\"parameters\": {\"rule\": {\"interval\": [{\"triggerAtHour\": 12}]}}, \"name\": \"12hs\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.1, \"position\": [-1220, 620]}, {\"parameters\": {\"amount\": 2, \"unit\": \"seconds\"}, \"name\": \"2s\", \"type\": \"n8n-nodes-base.wait\", \"typeVersion\": 1, \"position\": [60, 600]}, {\"parameters\": {\"phoneNumberId\": \"417653434771940\", \"recipientPhoneNumber\": \"=+{{ $node[\\\"Code1\\\"].json[\\\"telefonefinal\\\"] }}\", \"template\": \"msg_12h_salasecreta|pt_BR\", \"components\": {\"component\": [{\"type\": \"header\", \"headerParameters\": {\"parameter\": [{\"type\": \"image\", \"imageLink\": \"https://t3135484.p.clickup-attachments.com/t3135484/7ef5d402-22d9-4f17-addb-1a3383c17128/EST%2007.png\"}]}}]}}, \"name\": \"WhatsApp Business Cloud\", \"type\": \"n8n-nodes-base.whatsApp\", \"typeVersion\": 1, \"position\": [-140, 600]}, {\"parameters\": {\"phoneNumberId\": \"417653434771940\", \"recipientPhoneNumber\": \"=+5562981196678\", \"template\": \"disparo_finalizado|pt_BR\"}, \"name\": \"WhatsApp Business Cloud1\", \"type\": \"n8n-nodes-base.whatsApp\", \"typeVersion\": 1, \"position\": [-160, 400]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"id\": \"6e0dc768-c500-4f44-afeb-462ab225bf6a\", \"leftValue\": \"={{ $now.toFormat('dd/MM/yyyy') }}\", \"rightValue\": \"10/10/2024\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\", \"name\": \"filter.operator.equals\"}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"10/10\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2, \"position\": [-1040, 620]}, {\"parameters\": {\"operation\": \"limit\", \"keep\": \"lastItems\"}, \"name\": \"Item Lists\", \"type\": \"n8n-nodes-base.itemLists\", \"typeVersion\": 3, \"position\": [-320, 860]}, {\"parameters\": {\"documentId\": {\"__rl\": true, \"value\": \"1LphZOAErogV5YOb7oOMwAYVuMJD1Z9rtZOoHVVxAmVQ\", \"mode\": \"id\"}, \"sheetName\": {\"__rl\": true, \"value\": 441325879, \"mode\": \"list\", \"cachedResultName\": \"10/10/2024\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1LphZOAErogV5YOb7oOMwAYVuMJD1Z9rtZOoHVVxAmVQ/edit#gid=441325879\"}, \"options\": {}}, \"name\": \"le_planilha\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 3, \"position\": [-800, 1040]}, {\"parameters\": {\"options\": {}}, \"name\": \"separa_em_lotes\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [-560, 1040]}, {\"parameters\": {}, \"name\": \"N\\u00e3o faz nada\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [280, 1060]}, {\"parameters\": {\"jsCode\": \"const telefoneOriginal = $node[\\\"separa_em_lotes\\\"].json[\\\"telefone\\\"].toString();\\n\\n// Removendo caracteres n\\u00e3o num\\u00e9ricos\\nconst telefoneLimpo = telefoneOriginal.replace(/\\\\D/g, '');\\n\\n// Define o DDI padr\\u00e3o\\nconst ddiPadrao = '55';\\n\\n// Extrai o DDD e o n\\u00famero, considerando que o telefone limpo pode j\\u00e1 vir com o DDI\\nlet ddd, numero;\\nif (telefoneLimpo.startsWith(ddiPadrao)) {\\n    // Se o n\\u00famero j\\u00e1 come\\u00e7a com o DDI padr\\u00e3o, extrai sem o DDI\\n    ddd = telefoneLimpo.slice(2, 4);\\n    numero = telefoneLimpo.slice(4);\\n} else {\\n    // Se o n\\u00famero n\\u00e3o cont\\u00e9m o DDI, extrai o DDD e o n\\u00famero como est\\u00e3o\\n    ddd = telefoneLimpo.slice(0, 2);\\n    numero = telefoneLimpo.slice(2);\\n}\\n\\n// Monta o n\\u00famero de telefone final, adicionando o DDI apenas se n\\u00e3o estava presente\\nconst telefoneFinal = telefoneLimpo.startsWith(ddiPadrao) ? ddiPadrao + ddd + numero : ddiPadrao + telefoneLimpo;\\n\\nreturn { telefonefinal: telefoneFinal };\\n\"}, \"name\": \"Code\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 2, \"position\": [-320, 1060]}, {\"parameters\": {\"amount\": 2, \"unit\": \"seconds\"}, \"name\": \"2s1\", \"type\": \"n8n-nodes-base.wait\", \"typeVersion\": 1, \"position\": [80, 1060]}, {\"parameters\": {\"phoneNumberId\": \"417653434771940\", \"recipientPhoneNumber\": \"=+{{ $node[\\\"Code\\\"].json[\\\"telefonefinal\\\"] }}\", \"template\": \"msg_17h_salasecreta|pt_BR\"}, \"name\": \"WhatsApp Business Cloud2\", \"type\": \"n8n-nodes-base.whatsApp\", \"typeVersion\": 1, \"position\": [-120, 1060]}, {\"parameters\": {\"phoneNumberId\": \"417653434771940\", \"recipientPhoneNumber\": \"=+5562981196678\", \"template\": \"disparo_finalizado|pt_BR\"}, \"name\": \"WhatsApp Business Cloud3\", \"type\": \"n8n-nodes-base.whatsApp\", \"typeVersion\": 1, \"position\": [-140, 860]}, {\"parameters\": {\"rule\": {\"interval\": [{\"triggerAtHour\": 17}]}}, \"name\": \"17hs\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.1, \"position\": [-1200, 1080]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"id\": \"6e0dc768-c500-4f44-afeb-462ab225bf6a\", \"leftValue\": \"={{ $now.toFormat('dd/MM/yyyy') }}\", \"rightValue\": \"10/10/2024\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\", \"name\": \"filter.operator.equals\"}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"10/10/2024\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2, \"position\": [-1020, 1080]}], \"connections\": {\"Item Lists1\": {\"main\": [[{\"node\": \"WhatsApp Business Cloud1\", \"type\": \"main\", \"index\": 0}]]}, \"le_planilha1\": {\"main\": [[{\"node\": \"separa_em_lotes1\", \"type\": \"main\", \"index\": 0}]]}, \"separa_em_lotes1\": {\"main\": [[{\"node\": \"Item Lists1\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Code1\", \"type\": \"main\", \"index\": 0}]]}, \"N\\u00e3o faz nada1\": {\"main\": [[{\"node\": \"separa_em_lotes1\", \"type\": \"main\", \"index\": 0}]]}, \"Code1\": {\"main\": [[{\"node\": \"WhatsApp Business Cloud\", \"type\": \"main\", \"index\": 0}]]}, \"12hs\": {\"main\": [[{\"node\": \"10/10\", \"type\": \"main\", \"index\": 0}]]}, \"2s\": {\"main\": [[{\"node\": \"N\\u00e3o faz nada1\", \"type\": \"main\", \"index\": 0}]]}, \"WhatsApp Business Cloud\": {\"main\": [[{\"node\": \"2s\", \"type\": \"main\", \"index\": 0}]]}, \"10/10\": {\"main\": [[{\"node\": \"le_planilha1\", \"type\": \"main\", \"index\": 0}]]}, \"Item Lists\": {\"main\": [[{\"node\": \"WhatsApp Business Cloud3\", \"type\": \"main\", \"index\": 0}]]}, \"le_planilha\": {\"main\": [[{\"node\": \"separa_em_lotes\", \"type\": \"main\", \"index\": 0}]]}, \"separa_em_lotes\": {\"main\": [[{\"node\": \"Item Lists\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Code\", \"type\": \"main\", \"index\": 0}]]}, \"N\\u00e3o faz nada\": {\"main\": [[{\"node\": \"separa_em_lotes\", \"type\": \"main\", \"index\": 0}]]}, \"Code\": {\"main\": [[{\"node\": \"WhatsApp Business Cloud2\", \"type\": \"main\", \"index\": 0}]]}, \"2s1\": {\"main\": [[{\"node\": \"N\\u00e3o faz nada\", \"type\": \"main\", \"index\": 0}]]}, \"WhatsApp Business Cloud2\": {\"main\": [[{\"node\": \"2s1\", \"type\": \"main\", \"index\": 0}]]}, \"17hs\": {\"main\": [[{\"node\": \"10/10/2024\", \"type\": \"main\", \"index\": 0}]]}, \"10/10/2024\": {\"main\": [[{\"node\": \"le_planilha\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"AppTest n8n Onboarding Scaling Automation Solutions\", \"nodes\": [{\"parameters\": {\"path\": \"onboarding/n8n/tests/ScalingAutomationSolutions\", \"responseMode\": \"responseNode\", \"options\": {}}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 2, \"position\": [0, 0]}, {\"parameters\": {\"respondWith\": \"text\", \"responseBody\": \"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n  <link type=\\\"image/png\\\" sizes=\\\"16x16\\\" rel=\\\"icon\\\" href=\\\"https://i.postimg.cc/gJf9MgWR/icons8-32.png\\\">\\n  <meta charset=\\\"UTF-8\\\">\\n  <title>Scaling Automation Solutions</title>\\n  <link href=\\\"https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap\\\" rel=\\\"stylesheet\\\">\\n  <style>\\n    :root {\\n      --deep-blue: #081E2F;\\n      --primary-yellow: #FCB305;\\n      --red: #D32F2F;\\n      --red-hover: #C62828;\\n      --green: #2E7D32;\\n      --green-hover: #1B5E20;\\n      --blue: #197502;\\n      --blue-dark: #1555C0;\\n      --blue-light: #4245F5;\\n    }\\n    body {\\n      font-family: 'Open Sans', sans-serif;\\n      background: #F8F9FA;\\n      padding: 2rem;\\n      max-width: 800px;\\n      margin: 0 auto;\\n      min-height: 100vh;\\n    }\\n    #titlePage {\\n      text-align: center;\\n      padding: 3rem 1rem;\\n    }\\n    #titlePage h1 {\\n      font-size: 32px;\\n      color: var(--deep-blue);\\n      margin-bottom: 1.5rem;\\n    }\\n    .quiz-info {\\n      background: white;\\n      padding: 2rem;\\n      border-radius: 12px;\\n      box-shadow: 0 4px 12px rgba(8, 30, 47, 0.1);\\n      margin: 2rem auto;\\n      max-width: 500px;\\n    }\\n    .info-item {\\n      font-size: 16px;\\n      color: #4A4A4A;\\n      margin: 1rem 0;\\n      display: flex;\\n      justify-content: space-between;\\n      align-items: center;\\n    }\\n    #timer {\\n      position: fixed;\\n      top: 20px;\\n      right: 20px;\\n      background: var(--green);\\n      color: white;\\n      padding: 8px 16px;\\n      border-radius: 20px;\\n      font-weight: 600;\\n      box-shadow: 0 2px 6px rgba(46, 125, 50, 0.2);\\n    }\\n    .hidden {\\n      display: none;\\n    }\\n    .question {\\n      background: white;\\n      border-radius: 8px;\\n      padding: 1.5rem;\\n      margin-bottom: 1.5rem;\\n      box-shadow: 0 2px 8px rgba(8, 30, 47, 0.1);\\n      transition: all 0.3s ease;\\n    }\\n    .question h3 {\\n      font-size: 16px;\\n      font-weight: 600;\\n      color: var(--deep-blue);\\n      margin: 0 0 1rem 0;\\n    }\\n    label {\\n      display: block;\\n      margin: 0.5rem 0;\\n      padding: 0.75rem;\\n      border-radius: 4px;\\n      font-size: 14px;\\n      color: #4A4A4A;\\n      cursor: pointer;\\n      transition: all 0.2s ease;\\n    }\\n    label:hover {\\n      background: rgba(25, 118, 210, 0.04);\\n    }\\n    input[type=\\\"radio\\\"] {\\n      margin-right: 0.75rem;\\n      accent-color: var(--green);\\n      border-radius: 50%;\\n      width: 16px;\\n      height: 16px;\\n    }\\n    input[type=\\\"checkbox\\\"] {\\n      margin-right: 0.75rem;\\n      accent-color: var(--green);\\n    }\\n    input[type=\\\"text\\\"],\\n    input[type=\\\"number\\\"] {\\n      padding: 0.5rem;\\n      border: 1px solid #DEE2E6;\\n      border-radius: 4px;\\n      font-size: 14px;\\n      width: 100%;\\n      box-sizing: border-box;\\n    }\\n    input[disabled] {\\n      background: #f3f3f3;\\n      color: #888;\\n    }\\n    button {\\n      background: var(--green);\\n      color: white;\\n      border: none;\\n      padding: 12px 24px;\\n      border-radius: 6px;\\n      font-weight: 700;\\n      font-size: 15px;\\n      cursor: pointer;\\n      transition: all 0.3s ease;\\n      display: block;\\n      margin: 2rem auto 0;\\n      letter-spacing: 0.5px;\\n    }\\n    button:hover {\\n      background: var(--green-hover);\\n      transform: translateY(-2px);\\n      box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);\\n    }\\n    #result {\\n      background: white;\\n      padding: 1.5rem;\\n      border-radius: 8px;\\n      text-align: center;\\n      margin-top: 2rem;\\n      box-shadow: 0 2px 8px rgba(8, 30, 47, 0.1);\\n    }\\n    .correct {\\n      border-left: 4px solid var(--green);\\n      background: #F0F9FF;\\n    }\\n    .incorrect {\\n      border-left: 4px solid var(--red);\\n    }\\n    .correct-answer {\\n      background: #E8F5E9 !important;\\n      border: 1px solid var(--green);\\n      border-radius: 4px;\\n      padding: 2px 4px;\\n      margin-top: 0.5rem;\\n      display: inline-block;\\n    }\\n    @media (max-width: 600px) {\\n      body {\\n        padding: 1rem;\\n      }\\n      .question {\\n        padding: 1rem;\\n      }\\n      button {\\n        width: 100%;\\n        padding: 14px 20px;\\n      }\\n      #timer {\\n        top: 10px;\\n        right: 10px;\\n        font-size: 14px;\\n      }\\n    }\\n  </style>\\n</head>\\n<body>\\n  <!-- Title Page -->\\n  <div id=\\\"titlePage\\\">\\n    <h1>Scaling Automation Solutions</h1>\\n    <div class=\\\"quiz-info\\\">\\n      <div class=\\\"info-item\\\">\\n        <span>Questions:</span>\\n        <strong>15</strong>\\n      </div>\\n      <div class=\\\"info-item\\\">\\n        <span>Time Limit:</span>\\n        <strong>5 minutes</strong>\\n      </div>\\n      <div class=\\\"info-item\\\">\\n        <span>Passing Score:</span>\\n        <strong>80%</strong>\\n      </div>\\n      <button id=\\\"startExamBtn\\\" onclick=\\\"startQuiz()\\\">Start Exam</button>\\n    </div>\\n  </div>\\n  \\n  <!-- Quiz Container -->\\n  <div id=\\\"quizContainer\\\" class=\\\"hidden\\\">\\n    <div id=\\\"timer\\\">00:00</div>\\n    <h1>Scaling Automation Solutions</h1>\\n    <form id=\\\"examForm\\\">\\n      <!-- User Data Block (not scored) -->\\n      <div class=\\\"question\\\" data-points=\\\"0\\\">\\n        <h3>Enter Your Information</h3>\\n        <label>\\n          Your Name:\\n          <input type=\\\"text\\\" name=\\\"fullName\\\" placeholder=\\\"e.g., John Doe\\\">\\n        </label>\\n        <label>\\n          Your Profession:\\n          <input type=\\\"text\\\" name=\\\"profession\\\" value=\\\"All\\\" disabled>\\n        </label>\\n        <label>\\n          Your Recruiter:\\n          <select name=\\\"recruiter\\\">\\n            <option value=\\\"Anastasia Fadeeva\\\">Anastasia Fadeeva</option>\\n            <option value=\\\"Elena Ermakova\\\">Elena Ermakova</option>\\n            <option value=\\\"Anna Aleksandrova\\\">Anna Aleksandrova</option>\\n            <option value=\\\"Sabina Gasanova\\\">Sabina Gasanova</option>\\n          </select>\\n        </label>\\n      </div>\\n      \\n      <!-- Q1 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>1. Which of the following is a foundational strategy for workflow scaling, focusing on breaking down complex processes?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"A\\\"> Workflow Orchestration</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"B\\\"> Parameterization and Configuration</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> Modular Design</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"D\\\"> Asynchronous Processing</label>\\n      </div>\\n      \\n      <!-- Q2 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>2. What is the primary benefit of modular design in workflow scaling?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"A\\\"> It makes workflows harder to maintain.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"B\\\"> It creates monolithic, end-to-end workflows.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> It enhances maintainability and promotes reusability.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"D\\\"> It reduces workflow flexibility.</label>\\n      </div>\\n      \\n      <!-- Q3 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>3. What is the role of workflow orchestration in scaled automation?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q3\\\" value=\\\"A\\\"> To complicate the management of automated tasks.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q3\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> To manage, schedule, and monitor multiple workflows centrally.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q3\\\" value=\\\"C\\\"> To isolate workflows and prevent data sharing.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q3\\\" value=\\\"D\\\"> To eliminate the need for error handling.</label>\\n      </div>\\n      \\n      <!-- Q4 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>4. Externalizing data input paths and application credentials as configuration parameters in workflows is an example of:</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"A\\\"> Asynchronous Processing</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"B\\\"> Modular Design</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"C\\\"> Workflow Orchestration</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"D\\\" data-correct=\\\"true\\\"> Parameterization and Configuration</label>\\n      </div>\\n      \\n      <!-- Q5 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>5. Which processing technique allows workflows to initiate tasks and move on without waiting for immediate completion, enhancing throughput?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"A\\\"> Synchronous Processing</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"B\\\"> Modular Processing</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> Asynchronous Processing</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"D\\\"> Parameter-based Processing</label>\\n      </div>\\n      \\n      <!-- Q6 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>6. What is the main goal of load balancing in automation?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q6\\\" value=\\\"A\\\"> To overload single resources for efficiency.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q6\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> To distribute workload across multiple computing resources.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q6\\\" value=\\\"C\\\"> To complicate task distribution.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q6\\\" value=\\\"D\\\"> To limit the number of robots used.</label>\\n      </div>\\n      \\n      <!-- Q7 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>7. In which load balancing technique are tasks distributed sequentially to available resources in a cyclical order?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"A\\\"> Weighted Round-Robin</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"B\\\"> Resource-based Load Balancing</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> Round-Robin Distribution</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"D\\\"> Least Connection Load Balancing</label>\\n      </div>\\n      \\n      <!-- Q8 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>8. Which dynamic load balancing technique routes tasks to the resource with the fewest active connections?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"A\\\"> Round-Robin Distribution</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"B\\\"> Weighted Round-Robin</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"C\\\"> Resource-based Load Balancing</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"D\\\" data-correct=\\\"true\\\"> Least Connection Load Balancing</label>\\n      </div>\\n      \\n      <!-- Q9 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>9. What does \\\"right-sizing your infrastructure\\\" in resource optimization mean?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q9\\\" value=\\\"A\\\"> Over-provisioning resources for future growth.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q9\\\" value=\\\"B\\\"> Under-provisioning to minimize upfront costs.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q9\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> Accurately assessing resource needs and aligning infrastructure accordingly.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q9\\\" value=\\\"D\\\"> Ignoring resource requirements for automation.</label>\\n      </div>\\n      \\n      <!-- Q10 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>10. What benefit does elasticity and scalability in resource pools offer for automation?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"A\\\"> It leads to resource wastage and increased costs.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> It allows automatic adjustment of resource capacity based on demand.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"C\\\"> It requires manual adjustment of resources, increasing workload.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"D\\\"> It limits the number of tasks that can be processed.</label>\\n      </div>\\n      \\n      <!-- Q11 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>11. What is the function of a Center of Excellence (CoE) for automation within an enterprise?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q11\\\" value=\\\"A\\\"> To isolate automation efforts within a single team.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q11\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> To centralize expertise and guidance for automation initiatives across the enterprise.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q11\\\" value=\\\"C\\\"> To discourage automation adoption across the organization.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q11\\\" value=\\\"D\\\"> To create siloed automation efforts.</label>\\n      </div>\\n      \\n      <!-- Q12 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>12. Which of the following is a crucial element of a robust automation governance framework?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q12\\\" value=\\\"A\\\"> Lack of clear roles and responsibilities.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q12\\\" value=\\\"B\\\"> Absence of process for project approvals.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q12\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> Defined processes for project approvals and standards for security.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q12\\\" value=\\\"D\\\"> Ignoring security and compliance standards.</label>\\n      </div>\\n      \\n      <!-- Q13 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>13. Hyperautomation extends beyond RPA to include technologies like:</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q13\\\" value=\\\"A\\\"> Only Robotic Process Automation.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q13\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> AI, ML, Process Mining, and Low-Code Platforms.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q13\\\" value=\\\"C\\\"> Just traditional coding methods.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q13\\\" value=\\\"D\\\"> Manual processes and workflows.</label>\\n      </div>\\n      \\n      <!-- Q14 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>14. Cloud-native automation architectures leverage which technologies for scalability and resilience?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q14\\\" value=\\\"A\\\"> Legacy systems and monolithic applications.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q14\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> Containerization, microservices, and serverless computing.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q14\\\" value=\\\"C\\\"> On-premise servers exclusively.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q14\\\" value=\\\"D\\\"> Manual infrastructure management techniques.</label>\\n      </div>\\n      \\n      <!-- Q15 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>15. What role do process mining and automation discovery tools play in scaling automation?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q15\\\" value=\\\"A\\\"> To manually identify automation opportunities.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q15\\\" value=\\\"B\\\"> To complicate the process of automation identification.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q15\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> To automatically discover and analyze processes for automation opportunities.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q15\\\" value=\\\"D\\\"> To limit the scope of automation initiatives.</label>\\n      </div>\\n      \\n      <button id=\\\"submitBtn\\\" type=\\\"button\\\" onclick=\\\"calculateScore()\\\">Submit Exam</button>\\n    </form>\\n    <div id=\\\"result\\\"></div>\\n  </div>\\n  \\n  <script>\\n    // Fisher\\u2013Yates shuffle function\\n    function shuffle(array) {\\n      for (let i = array.length - 1; i > 0; i--) {\\n        const j = Math.floor(Math.random() * (i + 1));\\n        [array[i], array[j]] = [array[j], array[i]];\\n      }\\n      return array;\\n    }\\n  \\n    // Randomize answer labels (excluding the user data block)\\n    function randomizeAnswers() {\\n      const questions = document.querySelectorAll('.question[data-points]:not([data-points=\\\"0\\\"])');\\n      questions.forEach(question => {\\n        const labels = Array.from(question.querySelectorAll('label'));\\n        const shuffled = shuffle(labels);\\n        shuffled.forEach(label => label.parentNode.removeChild(label));\\n        const heading = question.querySelector('h3');\\n        shuffled.forEach(label => heading.parentNode.appendChild(label));\\n      });\\n    }\\n  \\n    let timer;\\n    let seconds = 0;\\n    const timeLimit = 300; // 5 minutes\\n    const examName = \\\"Scaling Automation Solutions\\\";\\n  \\n    function startQuiz() {\\n      document.getElementById('titlePage').classList.add('hidden');\\n      document.getElementById('quizContainer').classList.remove('hidden');\\n      randomizeAnswers();\\n      startTimer();\\n    }\\n  \\n    function startTimer() {\\n      timer = setInterval(() => {\\n        seconds++;\\n        const minutes = Math.floor(seconds / 60);\\n        const rem = seconds % 60;\\n        document.getElementById('timer').textContent =\\n          String(minutes).padStart(2, '0') + \\\":\\\" + String(rem).padStart(2, '0');\\n        if (seconds >= timeLimit) {\\n          clearInterval(timer);\\n          calculateScore(true);\\n        }\\n      }, 1000);\\n    }\\n  \\n    // Formats date as \\\"dd.mm.yyyy hh:mm\\\"\\n    function formatDate(date) {\\n      const options = {\\n        day: \\\"2-digit\\\",\\n        month: \\\"2-digit\\\",\\n        year: \\\"numeric\\\",\\n        hour: \\\"2-digit\\\",\\n        minute: \\\"2-digit\\\",\\n        hour12: false\\n      };\\n      const formatted = date.toLocaleString(\\\"en-GB\\\", options);\\n      return formatted.replace(/\\\\//g, \\\".\\\").replace(\\\", \\\", \\\" \\\");\\n    }\\n  \\n    function calculateScore(timeout = false) {\\n      const submitButton = document.getElementById('submitBtn');\\n      if (submitButton) {\\n        submitButton.style.display = 'none';\\n      }\\n      clearInterval(timer);\\n      let totalPoints = 0;\\n      let maxPoints = 0;\\n      const questions = document.querySelectorAll('.question');\\n      questions.forEach(question => {\\n        const points = parseInt(question.dataset.points) || 1;\\n        maxPoints += points;\\n        if (points === 0) return; // Skip user info block\\n        let correct = true;\\n        const inputs = question.querySelectorAll('input');\\n        let selectedValues = [];\\n        let correctValues = [];\\n        inputs.forEach(inp => {\\n          if (inp.checked) selectedValues.push(inp.value);\\n          if (inp.dataset.correct === \\\"true\\\") correctValues.push(inp.value);\\n        });\\n        if (\\n          selectedValues.length !== correctValues.length ||\\n          !selectedValues.every(val => correctValues.includes(val))\\n        ) {\\n          correct = false;\\n        }\\n        if (correct) {\\n          totalPoints += points;\\n          question.classList.add('correct');\\n        } else {\\n          question.classList.add('incorrect');\\n          inputs.forEach(inp => {\\n            if (inp.dataset.correct === \\\"true\\\") {\\n              inp.parentElement.classList.add('correct-answer');\\n            }\\n          });\\n        }\\n      });\\n      const percentage = ((totalPoints / maxPoints) * 100).toFixed(1);\\n      const timeUsed = Math.floor(seconds / 60) + \\\"m \\\" + (seconds % 60) + \\\"s\\\";\\n      let resultsHTML = \\n        `<h3>Exam Results</h3>\\n         <p>Your score: ${totalPoints} / ${maxPoints} (${percentage}%)</p>\\n         <p>Time used: ${timeUsed}</p>\\n         ${percentage >= 80 ? \\\"Congratulations! You passed!\\\" : \\\"Try again! Review your mistakes below.\\\"}\\n         ${timeout ? \\\"<p class='warning'>Time limit exceeded!</p>\\\" : \\\"\\\"}`;\\n      resultsHTML += \\n        `<button type=\\\"button\\\" onclick=\\\"location.reload()\\\">Retry</button>\\n         <button type=\\\"button\\\" onclick=\\\"window.location.href='#'\\\">Read Again</button>\\n         <button type=\\\"button\\\" id=\\\"taskButton\\\" onclick=\\\"loadTask()\\\">Task</button>`;\\n      document.getElementById('result').innerHTML = resultsHTML;\\n  \\n      const fullName = document.querySelector('[name=\\\"fullName\\\"]')?.value.trim() || \\\"\\\";\\n      const profession = document.querySelector('[name=\\\"profession\\\"]')?.value.trim() || \\\"\\\";\\n      const recruiter = document.querySelector('[name=\\\"recruiter\\\"]')?.value.trim() || \\\"\\\";\\n  \\n      // Post data to your webhook (Day remains \\\"5\\\")\\n      const postData = {\\n        fullName,\\n        profession,\\n        recruiter,\\n        day: \\\"5\\\",\\n        examName,\\n        scoreObtained: totalPoints,\\n        scoreTotal: maxPoints,\\n        timeUsed: seconds,\\n        timeTotal: timeLimit,\\n        date: formatDate(new Date())\\n      };\\n  \\n      try {\\n        window.scrollTo({ top: 0, behavior: 'smooth' });\\n        fetch(\\\"https://auto.crm-s.com/webhook/Onboarding/Update\\\", {\\n          method: \\\"POST\\\",\\n          headers: {\\n            \\\"Content-Type\\\": \\\"application/json\\\",\\n            \\\"Accept\\\": \\\"application/json, text/plain, */*\\\"\\n          },\\n          body: JSON.stringify(postData),\\n          mode: \\\"cors\\\"\\n        })\\n        .then(async (res) => {\\n          console.log(\\\"POST response status:\\\", res.status);\\n          const text = await res.text();\\n          console.log(\\\"POST response body:\\\", text);\\n        })\\n        .catch(err => {\\n          console.error(\\\"Error in POST:\\\", err);\\n        });\\n      } catch (error) {\\n        console.error(\\\"Error submitting quiz results:\\\", error);\\n      }\\n    }\\n  \\n    // loadTask() replaces the quiz view with task instructions\\n    function loadTask() {\\n      const taskMarkdown = `# \\ud83d\\udee0 Scaling Automation Solutions - Practice Task\\n\\nDevelop a scenario that demonstrates how to implement strategies for scaling automation solutions to improve efficiency and adaptability. Focus on:\\n- Identifying bottlenecks in current automation workflows and proposing mitigation strategies.\\n- Outlining the configuration of scalable modules, including modular design and load balancing techniques.\\n- Describing how dynamic resource allocation and asynchronous processing enhance performance.\\n- Reflecting on the benefits of a scalable automation framework in optimizing operations.\\n\\n## Steps:\\n1. **Workflow Analysis Challenge**: Describe a current automation workflow that could benefit from scaling.\\n2. **Scaling Strategy**: Outline your plan for implementing scalable automation solutions, including modular design and load balancing.\\n3. **Technical Details**: Specify key parameters, tools, and error handling mechanisms involved in your solution.\\n4. **Impact Evaluation**: Explain how scaling automation solutions improves efficiency, adaptability, and overall performance.\\n\\nWhen finished, mark the checkbox and optionally provide a link to your detailed plan.`;\\n  \\n      const parsedMarkdown = parseMarkdown(taskMarkdown);\\n      document.body.innerHTML = \\n        `<div style=\\\"font-family: 'Open Sans', sans-serif; background: #F8F9FA; padding: 2rem; max-width: 800px; margin: 0 auto;\\\">\\n          ${parsedMarkdown}\\n          <div style=\\\"margin-top: 2rem;\\\">\\n            <label for=\\\"taskLinkInput\\\" style=\\\"font-weight: bold;\\\">Optional link to your work:</label>\\n            <input type=\\\"text\\\" id=\\\"taskLinkInput\\\" placeholder=\\\"https://docs.google.com/...\\\" style=\\\"width: 100%; padding: 8px; margin: 4px 0 1rem;\\\">\\n            <input type=\\\"checkbox\\\" id=\\\"taskCompletedCheckbox\\\">\\n            <label for=\\\"taskCompletedCheckbox\\\">Task Completed</label>\\n          </div>\\n        </div>`;\\n      const checkbox = document.getElementById(\\\"taskCompletedCheckbox\\\");\\n      checkbox.addEventListener(\\\"change\\\", function() {\\n        if (checkbox.checked) {\\n          const taskLink = document.getElementById(\\\"taskLinkInput\\\").value.trim() || \\\"\\\";\\n          checkbox.disabled = true;\\n          try {\\n            fetch(\\\"https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable\\\", {\\n              method: \\\"POST\\\",\\n              headers: {\\n                \\\"Content-Type\\\": \\\"application/json\\\",\\n                \\\"Accept\\\": \\\"application/json, text/plain, */*\\\"\\n              },\\n              body: JSON.stringify({ link: taskLink, date: formatDate(new Date()) }),\\n              mode: \\\"cors\\\"\\n            })\\n            .then(async (res) => {\\n              console.log(\\\"Task completed webhook status:\\\", res.status);\\n              const text = await res.text();\\n              console.log(\\\"Task completed webhook body:\\\", text);\\n            })\\n            .catch(err => {\\n              console.error(\\\"Error sending task completed webhook:\\\", err);\\n              alert(\\\"Your task has been marked as complete, but there was an issue connecting to the server. Your instructor will be notified.\\\");\\n            });\\n          } catch (error) {\\n            console.error(\\\"Error marking task as complete:\\\", error);\\n            alert(\\\"Your task has been marked as complete, but there was an issue connecting to the server. Your instructor will be notified.\\\");\\n          }\\n        }\\n      });\\n    }\\n  \\n    // Markdown parser: converts headings (#, ##, ###, ####) into <details> blocks\\n    function parseMarkdown(markdownText) {\\n      const lines = markdownText.split('\\\\n');\\n      let html = \\\"\\\";\\n      const stack = [];\\n      lines.forEach(line => {\\n        const match = line.match(/^(#{1,4})\\\\s+(.*)/);\\n        if (match) {\\n          const level = match[1].length;\\n          const text = match[2];\\n          while (stack.length && stack[stack.length - 1] >= level) {\\n            html += \\\"</details>\\\";\\n            stack.pop();\\n          }\\n          html += `<details><summary>${text}</summary>`;\\n          stack.push(level);\\n        } else {\\n          html += line + \\\"<br>\\\";\\n        }\\n      });\\n      while (stack.length) {\\n        html += \\\"</details>\\\";\\n        stack.pop();\\n      }\\n      return html;\\n    }\\n  </script>\\n</body>\\n</html>\\n\", \"options\": {\"responseCode\": 200, \"responseHeaders\": {\"entries\": [{\"name\": \"Content-type\", \"value\": \"text/html\"}]}}}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1.1, \"position\": [220, 0]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"AppTest n8n Onboarding Scaling Automation Solutions\", \"nodes\": [{\"parameters\": {\"path\": \"onboarding/n8n/tests/ScalingAutomationSolutions\"}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"respondWith\": \"text\", \"responseBody\": \"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <title>Scaling Automation Solutions</title>\\n    <link href=\\\"https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap\\\" rel=\\\"stylesheet\\\">\\n    <style>\\n        body {\\n            font-family: 'Roboto', sans-serif;\\n            background: #f9f9f9;\\n            padding: 2rem;\\n            max-width: 800px;\\n            margin: 0 auto;\\n        }\\n        .question {\\n            background: white;\\n            padding: 1.5rem;\\n            border-radius: 8px;\\n            margin-bottom: 1.5rem;\\n            box-shadow: 0 2px 6px rgba(0,0,0,0.1);\\n        }\\n        label {\\n            display: block;\\n            margin: 0.5rem 0 0.3rem;\\n            font-weight: 500;\\n        }\\n        select, input {\\n            padding: 0.6rem;\\n            border: 1px solid #ccc;\\n            border-radius: 4px;\\n            width: 100%;\\n            box-sizing: border-box;\\n        }\\n        button {\\n            background: #4a90e2;\\n            color: white;\\n            border: none;\\n            padding: 1rem 1.5rem;\\n            border-radius: 4px;\\n            font-weight: bold;\\n            cursor: pointer;\\n            transition: background 0.3s ease;\\n            display: block;\\n            margin: 1.5rem auto 0;\\n        }\\n        button:hover {\\n            background: #357abd;\\n        }\\n        h1 {\\n            color: #333;\\n            text-align: center;\\n            margin-bottom: 1.5rem;\\n        }\\n        p {\\n            color: #555;\\n            line-height: 1.6;\\n            margin-bottom: 1.5rem;\\n        }\\n        .task {\\n            background: white;\\n            padding: 1.5rem;\\n            border-radius: 8px;\\n            margin-bottom: 1.5rem;\\n            box-shadow: 0 2px 6px rgba(0,0,0,0.1);\\n        }\\n        textarea {\\n            width: 100%;\\n            height: 120px;\\n            padding: 0.6rem;\\n            border: 1px solid #ccc;\\n            border-radius: 4px;\\n            box-sizing: border-box;\\n        }\\n    </style>\\n</head>\\n<body>\\n    <h1>Scaling Automation Solutions</h1>\\n    <p>Test your knowledge on scaling automation solutions in n8n. Answer the questions below to complete this section.</p>\\n\\n    <div class=\\\"question\\\">\\n        <label for=\\\"q1\\\">1. What is the primary benefit of using n8n for automation scaling?</label>\\n        <select id=\\\"q1\\\">\\n            <option value=\\\"\\\">Select an answer</option>\\n            <option value=\\\"A\\\">A) It allows for manual execution of workflows</option>\\n            <option value=\\\"B\\\">B) It provides a visual interface for workflow creation</option>\\n            <option value=\\\"C\\\">C) It enables automation of complex, multi-step processes</option>\\n            <option value=\\\"D\\\">D) It allows for integration with external APIs</option>\\n        </select>\\n    </div>\\n\\n    <div class=\\\"question\\\">\\n        <label for=\\\"q2\\\">2. Which of the following is a key feature of n8n that supports automation scaling?</label>\\n        <select id=\\\"q2\\\">\\n            <option value=\\\"\\\">Select an answer</option>\\n            <option value=\\\"A\\\">A) Manual scheduling</option>\\n            <option value=\\\"B\\\">B) Node-based workflow design</option>\\n            <option value=\\\"C\\\">C) Integration with external databases</option>\\n            <option value=\\\"D\\\">D) All of the above</option>\\n        </select>\\n    </div>\\n\\n    <div class=\\\"question\\\">\\n        <label for=\\\"q3\\\">3. How does n8n handle workflow execution when scaling automation?</label>\\n        <select id=\\\"q3\\\">\\n            <option value=\\\"\\\">Select an answer</option>\\n            <option value=\\\"A\\\">A) It executes workflows sequentially</option>\\n            <option value=\\\"B\\\">B) It uses a queue system for workflow execution</option>\\n            <option value=\\\"C\\\">C) It executes workflows in parallel</option>\\n            <option value=\\\"D\\\">D) It uses a manual trigger for execution</option>\\n        </select>\\n    </div>\\n\\n    <div class=\\\"question\\\">\\n        <label for=\\\"q4\\\">4. What is a common use case for automation scaling in n8n?</label>\\n        <select id=\\\"q4\\\">\\n            <option value=\\\"\\\">Select an answer</option>\\n            <option value=\\\"A\\\">A) Automating social media posts</option>\\n            <option value=\\\"B\\\">B) Automating data entry tasks</option>\\n            <option value=\\\"C\\\">C) Automating customer service responses</option>\\n            <option value=\\\"D\\\">D) All of the above</option>\\n        </select>\\n    </div>\\n\\n    <div class=\\\"question\\\">\\n        <label for=\\\"q5\\\">5. Which of the following is a limitation of automation scaling in n8n?</label>\\n        <select id=\\\"q5\\\">\\n            <option value=\\\"\\\">Select an answer</option>\\n            <option value=\\\"A\\\">A) It requires manual configuration</option>\\n            <option value=\\\"B\\\">B) It can be resource-intensive</option>\\n            <option value=\\\"C\\\">C) It cannot handle complex workflows</option>\\n            <option value=\\\"D\\\">D) It lacks integration capabilities</option>\\n        </select>\\n    </div>\\n\\n    <div class=\\\"task\\\">\\n        <p>Task: Describe a scenario where automation scaling in n8n would be beneficial for a business. Explain how n8n's features support this scenario.</p>\\n        <textarea id=\\\"taskInput\\\" placeholder=\\\"Enter your answer here...\\\"></textarea>\\n    </div>\\n\\n    <button onclick=\\\"submitQuiz()\\\">Submit Quiz</button>\\n    <button onclick=\\\"submitTask()\\\">Submit Task</button>\\n\\n    <script>\\n        async function submitQuiz() {\\n            const answers = {};\\n            const questions = document.querySelectorAll('.question');\\n            questions.forEach(q => {\\n                const select = q.querySelector('select');\\n                answers[q.dataset.index] = select.value;\\n            });\\n            try {\\n                const response = await fetch('https://auto.crm-s.com/webhook/Onboarding/Update', {\\n                    method: 'POST',\\n                    headers: {'Content-Type': 'application/json'},\\n                    body: JSON.stringify({ quizData: answers })\\n                });\\n                console.log('Quiz data sent:', answers);\\n            } catch (error) {\\n                console.error('Error sending quiz data:', error);\\n            }\\n        }\\n\\n        async function submitTask() {\\n            const task = document.getElementById('taskInput').value;\\n            try {\\n                const response = await fetch('https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable', {\\n                    method: 'POST',\\n                    headers: {'Content-Type': 'application/json'},\\n                    body: JSON.stringify({ taskData: task })\\n                });\\n                console.log('Task data sent:', task);\\n            } catch (error) {\\n                console.error('Error sending task data:', error);\\n            }\\n        }\\n    </script>\\n</body>\\n</html>\"}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1, \"position\": [220, 0]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false, \"tags\": [{\"name\": \"n8n\"}, {\"name\": \"Tests\"}, {\"name\": \"Day5\"}, {\"name\": \"Onboarding\"}]}"}, {"ground_truth_json": "{\"name\": \"Trade Recommendations\", \"nodes\": [{\"parameters\": {\"rule\": {\"interval\": [{\"triggerAtHour\": 9, \"triggerAtMinute\": 45}]}}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.2, \"position\": [-480, -40]}, {\"parameters\": {\"promptType\": \"define\", \"text\": \"={{ $json.Microsoft }}{{ $json.Meta }}{{ $json.Nvidia }}{{ $json['Schwab Dividend ETF'] }}{{ $json['Vanguard SP500'] }}{{ $json['Vanguard Total Market'] }}\", \"options\": {\"systemMessage\": \"# Stock Trading Analysis Agent\\n\\nYou are an advanced AI agent specialized in stock market analysis. Your task is to analyze a given table of exchange/stock pairs and provide buy, sell, or hold recommendations based on current trading day data and news analysis.\\n\\n## Your Capabilities\\n\\nYou have access to the following tools:\\n\\n1. **News Analysis Tools**:\\n   - Wiki News headlines scraper\\n   - CNN news headlines API\\n   - NewsAPI for comprehensive headline coverage\\n\\n2. **Stock Analysis Tools**:\\n   - NYSE Stock Analysis Tool (for NYSE-listed stocks)\\n   - NASDAQ Stock Analysis Tool (for NASDAQ-listed stocks)\\n   - NYSE Arca Stock Analysis Tool (for NYSE Arac listed ETFs)\\n\\n## Input Format\\n\\nYou will receive a table of exchange/stock pairs in the following format:\\n\\n```\\nExchange | Stock Symbol\\n---------|-------------\\nNYSE     | DIS         \\nNASDAQ   | AAPL        \\n```\\n\\n## Your Task Workflow\\n\\nFor each stock in the input table, perform the following analysis steps:\\n\\n1. **Identify Exchange and Use Appropriate Tool**:\\n   - For NYSE stocks, use the NYSE Stock Analysis Tool\\n   - For NASDAQ stocks, use the NASDAQ Stock Analysis Tool\\n   - For NYSE Arca ETFs, use the NYSE Arca Stock Analysis Tool\\n\\n2. **Gather Current Trading Data**:\\n   - Retrieve current stock price\\n   - Get day's high and low\\n   - Calculate day's trading volume compared to 30-day average\\n   - Identify key technical indicators (50-day and 200-day moving averages, RSI, MACD)\\n   - Note any unusual trading patterns or volume spikes\\n\\n3. **News Sentiment Analysis**:\\n   - Search for company-specific news across all news sources\\n   - Search for industry-related news\\n   - Search for market-wide news that might impact the stock\\n   - Analyze sentiment (positive, negative, neutral) of headlines\\n   - Identify any breaking news that could significantly impact trading\\n\\n4. **Fundamental Analysis**:\\n   - Check for recent earnings reports or upcoming earnings dates\\n   - Review recent analyst ratings changes\\n   - Check for dividend announcements\\n   - Review P/E ratio compared to industry average\\n\\n5. **Generate Recommendation**:\\n   - Synthesize all data points into a BUY, SELL, or HOLD recommendation\\n   - Provide confidence level (HIGH, MEDIUM, LOW)\\n   - List top 3 factors influencing your decision\\n\\n## Output Format\\n\\nFor each stock, provide your analysis in the following format:\\n\\n```\\nANALYSIS FOR: [STOCK SYMBOL] ([COMPANY NAME])\\nEXCHANGE: [EXCHANGE]\\nCURRENT PRICE: $[PRICE] ([PERCENTAGE CHANGE])\\nTRADING VOLUME: [VOLUME] ([PERCENTAGE VS 30-DAY AVG])\\nKEY TECHNICALS: [SUMMARY OF TECHNICAL INDICATORS]\\nNEWS SENTIMENT: [POSITIVE/NEGATIVE/NEUTRAL] - [BRIEF EXPLANATION]\\nFUNDAMENTAL OUTLOOK: [BRIEF SUMMARY]\\nRECOMMENDATION: [BUY/SELL/HOLD] (CONFIDENCE: [HIGH/MEDIUM/LOW])\\nPRIMARY FACTORS:\\n1. [FACTOR 1]\\n2. [FACTOR 2]\\n3. [FACTOR 3]\\nADDITIONAL NOTES: [ANY SPECIAL CONSIDERATIONS]\\n```\\n\\n## Additional Instructions\\n\\n1. **Time Sensitivity**: Prioritize breaking news and unusual trading patterns that have emerged during the current trading day.\\n\\n2. **Contextual Analysis**: Consider broader market trends - is this a sector-wide movement or stock-specific?\\n\\n3. **Risk Assessment**: Include any notable risk factors that could impact your recommendation.\\n\\n4. **Technical Analysis Weight**: For volatile stocks, weight technical indicators more heavily. For stable blue-chip stocks, weight fundamentals more heavily.\\n\\n5. **Confirmation Patterns**: Look for confirmation across multiple data sources before making high-confidence recommendations.\\n\\n6. **Contrary Indicators**: Explicitly note when you find conflicting signals and explain your reasoning for your final recommendation.\\n\\n7. **Transparency**: Always list the specific data points that led to your recommendation.\\n\\n## Example Process\\n\\n1. Receive stock: NYSE | KO | Coca-Cola Company\\n2. Use NYSE Stock Analysis Tool to gather trading data\\n3. Scan all news sources for \\\"Coca-Cola\\\" or \\\"KO\\\" related headlines\\n4. Analyze beverage industry news for relevant context\\n5. Check for earnings reports or analyst actions\\n6. Synthesize data to form recommendation\\n7. Format output according to the specification\\n\\nAlways provide reasoning that would be valuable to both novice and experienced investors. Focus on actionable insights rather than just data reporting.\"}}, \"name\": \"AI Agent\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.7, \"position\": [-80, -40]}, {\"parameters\": {\"model\": \"claude-3-5-sonnet-20241022\", \"options\": {}}, \"name\": \"Anthropic Chat Model\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatAnthropic\", \"typeVersion\": 1.2, \"position\": [-160, 180]}, {\"parameters\": {\"name\": \"NASDAQ\", \"description\": \"The NASDAQ Stock Analysis Tool provides comprehensive financial data analysis capabilities for stocks traded on the NASDAQ exchange. This tool enables automated workflows to gather, analyze, and visualize stock market data, helping users make informed investment decisions.\", \"workflowId\": {\"__rl\": true, \"value\": \"dsJL3YGPqIm5nDDS\", \"mode\": \"list\", \"cachedResultName\": \"Technical Stock Analyst v2 NASDAQ\"}}, \"name\": \"NASDAQ Stock Analysis Tool\", \"type\": \"@n8n/n8n-nodes-langchain.toolWorkflow\", \"typeVersion\": 1.3, \"position\": [400, 180]}, {\"parameters\": {\"name\": \"NYSE\", \"description\": \"The NYSE Stock Analysis Tool provides comprehensive financial data analysis capabilities for stocks traded on the NYSE exchange. This tool enables automated workflows to gather, analyze, and visualize stock market data, helping users make informed investment decisions.\", \"workflowId\": {\"__rl\": true, \"value\": \"EZGUZkOScAYwc2lH\", \"mode\": \"list\", \"cachedResultName\": \"Technical Stock Analyst v2 NYSE\"}}, \"name\": \"NYSE Stock Analysis Tool\", \"type\": \"@n8n/n8n-nodes-langchain.toolWorkflow\", \"typeVersion\": 1.3, \"position\": [400, 340]}, {\"parameters\": {\"chatId\": \"XXXXXXXXXXXXXXXXXXXXX\", \"text\": \"={{ $json.output }}\", \"additionalFields\": {}}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [400, -180]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"a98b6a16-ba95-49a3-9106-8188fff5ec63\", \"name\": \"Microsoft\", \"value\": \"NASDAQ:MSFT\", \"type\": \"string\"}, {\"id\": \"c659d8e3-de8e-47c6-9ebb-45f233643e9c\", \"name\": \"Meta\", \"value\": \"NASDAQ:META\", \"type\": \"string\"}, {\"id\": \"5d1d5b90-e99d-438f-8691-88cbff52ab9c\", \"name\": \"Nvidia\", \"value\": \"NASDAQ:NVDA\", \"type\": \"string\"}, {\"id\": \"973d6861-440e-4db2-9e25-3494d2634f4e\", \"name\": \"Schwab Dividend ETF\", \"value\": \"AMEX:SCHD\", \"type\": \"string\"}, {\"id\": \"c43ce796-418e-408d-a238-ed592b471479\", \"name\": \"Vanguard SP500\", \"value\": \"AMEX:VOO\", \"type\": \"string\"}, {\"id\": \"cf0c26b7-0371-431e-a0d5-b15688a543b7\", \"name\": \"Vanguard Total Market\", \"value\": \"AMEX:VTI\", \"type\": \"string\"}]}, \"options\": {}}, \"name\": \"Edit Fields\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [-280, -40]}, {\"parameters\": {\"name\": \"NewsAPI\", \"description\": \"Call this tool to use the NewsAPI to gather the latest news headlines\", \"workflowId\": {\"__rl\": true, \"value\": \"73n2334x1LFCBDFf\", \"mode\": \"list\", \"cachedResultName\": \"NewsAPI\"}}, \"name\": \"NewsAPI Tool\", \"type\": \"@n8n/n8n-nodes-langchain.toolWorkflow\", \"typeVersion\": 1.3, \"position\": [260, 260]}, {\"parameters\": {\"toolDescription\": \"Use this tool to get current news headlines from Wiki News\", \"url\": \"https://en.m.wikipedia.org/wiki/Portal:Current_events\"}, \"name\": \"Wiki Current Events\", \"type\": \"@n8n/n8n-nodes-langchain.toolHttpRequest\", \"typeVersion\": 1.1, \"position\": [120, 260]}, {\"parameters\": {\"toolDescription\": \"Get current headlines from CNN\", \"url\": \"https://lite.cnn.com/\"}, \"name\": \"CNN Headlines\", \"type\": \"@n8n/n8n-nodes-langchain.toolHttpRequest\", \"typeVersion\": 1.1, \"position\": [-20, 260]}, {\"parameters\": {\"sendTo\": \"XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX\", \"subject\": \"=Stock Analysis:  {{ $('Schedule Trigger').item.json['Readable date'] }}\", \"emailType\": \"text\", \"message\": \"={{ $json.output }}\", \"options\": {}}, \"name\": \"Gmail\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [400, -20]}, {\"parameters\": {\"name\": \"Arca\", \"description\": \"The NYSE Arca Stock Analysis Tool provides comprehensive financial data analysis capabilities for stocks traded on the NYSE Arca exchange. This tool enables automated workflows to gather, analyze, and visualize stock market data, helping users make informed investment decisions.\", \"workflowId\": {\"__rl\": true, \"value\": \"DQg8ZkQahZKli48m\", \"mode\": \"list\", \"cachedResultName\": \"Technical Stock Analyst v2 NYSE Arca\"}}, \"name\": \"NYSE Arca Stock Analysis Tool\", \"type\": \"@n8n/n8n-nodes-langchain.toolWorkflow\", \"typeVersion\": 1.3, \"position\": [540, 260]}], \"connections\": {\"Schedule Trigger\": {\"main\": [[{\"node\": \"Edit Fields\", \"type\": \"main\", \"index\": 0}]]}, \"Anthropic Chat Model\": {\"ai_languageModel\": [[{\"node\": \"AI Agent\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"NASDAQ Stock Analysis Tool\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"NYSE Stock Analysis Tool\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"AI Agent\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Gmail\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields\": {\"main\": [[{\"node\": \"AI Agent\", \"type\": \"main\", \"index\": 0}]]}, \"NewsAPI Tool\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"Wiki Current Events\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"CNN Headlines\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"NYSE Arca Stock Analysis Tool\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Stock Trade Recommendations\", \"nodes\": [{\"parameters\": {\"cronExpression\": \"0 9 45 * * 1-5\"}, \"name\": \"Schedule\", \"type\": \"n8n-nodes-base.cron\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"systemMessage\": \"You are a financial analyst tasked with providing daily stock trade recommendations. You will analyze the following assets: Microsoft, Meta, Nvidia, Schwab Dividend ETF, Vanguard SP500, and Vanguard Total Market. You will use specialized tools for stock analysis on the NASDAQ, NYSE, and NYSE Arca exchanges, as well as news sources. Your recommendations should be concise and include a rationale for each trade.\"}, \"name\": \"Anthropic Agent\", \"type\": \"n8n-nodes-base.anthropicAgent\", \"typeVersion\": 1, \"position\": [220, 0]}, {\"parameters\": {\"stock\": \"NASDAQ\"}, \"name\": \"Stock Analysis\", \"type\": \"n8n-nodes-base.stockAnalysis\", \"typeVersion\": 1, \"position\": [440, 0]}, {\"parameters\": {\"stock\": \"NYSE\"}, \"name\": \"Stock Analysis1\", \"type\": \"n8n-nodes-base.stockAnalysis\", \"typeVersion\": 1, \"position\": [660, 0]}, {\"parameters\": {\"stock\": \"NYSE Arca\"}, \"name\": \"Stock Analysis2\", \"type\": \"n8n-nodes-base.stockAnalysis\", \"typeVersion\": 1, \"position\": [880, 0]}, {\"parameters\": {\"subject\": \"Stock Trade Recommendations\", \"message\": \"={{ $json.output }}\"}, \"name\": \"Email\", \"type\": \"n8n-nodes-base.email\", \"typeVersion\": 1, \"position\": [1100, 0]}, {\"parameters\": {\"tool\": [{\"name\": \"NASDAQ\", \"description\": \"Provides stock analysis for the NASDAQ exchange.\"}, {\"name\": \"NYSE\", \"description\": \"Provides stock analysis for the NYSE exchange.\"}, {\"name\": \"NYSE Arca\", \"description\": \"Provides stock analysis for the NYSE Arca exchange.\"}, {\"name\": \"News\", \"description\": \"Fetches news articles from various sources.\"}]}, \"name\": \"Tools\", \"type\": \"n8n-nodes-base.tools\", \"typeVersion\": 1, \"position\": [1320, 0]}], \"connections\": {\"Schedule\": {\"main\": [[{\"node\": \"Anthropic Agent\", \"type\": \"main\", \"index\": 0}]]}, \"Anthropic Agent\": {\"main\": [[{\"node\": \"Email\", \"type\": \"main\", \"index\": 0}]]}, \"Stock Analysis\": {\"main\": [[{\"node\": \"Anthropic Agent\", \"type\": \"main\", \"index\": 0}]]}, \"Stock Analysis1\": {\"main\": [[{\"node\": \"Anthropic Agent\", \"type\": \"main\", \"index\": 0}]]}, \"Stock Analysis2\": {\"main\": [[{\"node\": \"Anthropic Agent\", \"type\": \"main\", \"index\": 0}]]}, \"Tools\": {\"main\": [[{\"node\": \"Anthropic Agent\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Multiple trigger node rerun\", \"nodes\": [{\"parameters\": {}, \"name\": \"When clicking \\u2018Test workflow\\u2019\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [460, 460]}, {\"parameters\": {\"url\": \"https://internal.users.n8n.cloud/webhook/random-data-api\", \"options\": {}}, \"name\": \"fetch 5 random users\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [680, 460]}, {\"parameters\": {\"jsCode\": \"// Loop over input items and add a new field called 'myNewField' to the JSON of each one\\nfor (const item of $input.all()) {\\n  item.json.first_name_reversed = item.json = {\\n    firstName: item.json.firstname,\\n    firstnNameReversed: item.json.firstname.split(\\\"\\\").reverse().join(\\\"\\\")\\n  };\\n}\\n\\nreturn $input.all();\"}, \"name\": \"do something with them\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 2, \"position\": [900, 460]}, {\"parameters\": {\"rule\": {\"interval\": [{\"field\": \"cronExpression\", \"expression\": \"* * * * *\"}]}}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.1, \"position\": [460, 660]}], \"connections\": {\"When clicking \\u2018Test workflow\\u2019\": {\"main\": [[{\"node\": \"fetch 5 random users\", \"type\": \"main\", \"index\": 0}]]}, \"fetch 5 random users\": {\"main\": [[{\"node\": \"do something with them\", \"type\": \"main\", \"index\": 0}]]}, \"Schedule Trigger\": {\"main\": [[{\"node\": \"fetch 5 random users\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Multiple trigger node rerun\", \"nodes\": [{\"parameters\": {}, \"name\": \"On clicking 'execute'\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [100, 300]}, {\"parameters\": {\"url\": \"https://internal.users.n8n.cloud/webhook/random-data-api\"}, \"name\": \"HTTP Request\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 1, \"position\": [300, 300]}, {\"parameters\": {\"interval\": [{\"field\": \"minutes\", \"minutesInterval\": 1}]}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1, \"position\": [100, 500]}, {\"parameters\": {\"jsCode\": \"for (item of items) {\\n  item.firstname = item.firstname.split('').reverse().join('');\\n}\\n\\nreturn items;\"}, \"name\": \"Code\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 1, \"position\": [500, 300]}], \"connections\": {\"On clicking 'execute'\": {\"main\": [[{\"node\": \"HTTP Request\", \"type\": \"main\", \"index\": 0}]]}, \"Schedule Trigger\": {\"main\": [[{\"node\": \"HTTP Request\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request\": {\"main\": [[{\"node\": \"Code\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Custom_Engine_Scrape\", \"nodes\": [{\"parameters\": {}, \"name\": \"When clicking \\u2018Test workflow\\u2019\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [460, 460]}, {\"parameters\": {\"url\": \"=https://www.googleapis.com/customsearch/v1?key=YOUR_API_KEY&cx=ENGINE_ID&q=CEO+real+estate+Chicago&num=10&start={{ $json.start }}\\n\", \"sendHeaders\": true, \"headerParameters\": {\"parameters\": [{\"name\": \"User-Agent\", \"value\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3\"}]}, \"options\": {}}, \"name\": \"HTTP Request\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [1040, 480]}, {\"parameters\": {\"jsCode\": \"let start = 0;  // Initial page\\nconst limit = 100;  // Limit to fetch 500 profiles (adjust as needed)\\nconst batchSize = 10;  // Google Search returns 100 results per page\\nconst output = [];\\n\\n// Loop through pages by increments of 100\\nwhile (start < limit) {\\n  output.push({ json: { start: start } });\\n  start += batchSize;  // Increment by 100 for each page\\n}\\n\\nreturn output;\\n\"}, \"name\": \"Pagination\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 2, \"position\": [600, 460]}, {\"parameters\": {\"options\": {}}, \"name\": \"Loop Over Items\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [820, 460]}, {\"parameters\": {\"jsCode\": \"// Get the items array from the response\\nconst items = $json[\\\"items\\\"];  // Access the items array directly from the response\\n\\n// Create an empty array to store the extracted URLs\\nconst output = [];\\n\\nif (items && items.length > 0) {\\n  // Loop over each item in the array and extract the 'link'\\n  for (const item of items) {\\n    if (item.link) {\\n      // Push each extracted URL as a separate item in the output\\n      output.push({\\n        json: {\\n          url: item.link,\\n        }\\n      });\\n    }\\n  }\\n}\\n\\nreturn output;  // Return the array of URL items\\n\"}, \"name\": \"Code\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 2, \"position\": [1240, 480]}, {\"parameters\": {\"operation\": \"append\", \"documentId\": {\"__rl\": true, \"value\": \"1_0bfvmyLdhc-j8QhCDDRVZQZvk5TNg7TYoUsvyh9MvY\", \"mode\": \"list\", \"cachedResultName\": \"Profiles Demo\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1_0bfvmyLdhc-j8QhCDDRVZQZvk5TNg7TYoUsvyh9MvY/edit?usp=drivesdk\"}, \"sheetName\": {\"__rl\": true, \"value\": \"gid=0\", \"mode\": \"list\", \"cachedResultName\": \"Sheet1\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1_0bfvmyLdhc-j8QhCDDRVZQZvk5TNg7TYoUsvyh9MvY/edit#gid=0\"}, \"columns\": {\"mappingMode\": \"autoMapInputData\", \"value\": {}, \"matchingColumns\": [], \"schema\": []}, \"options\": {}}, \"name\": \"Google Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.5, \"position\": [1420, 480]}], \"connections\": {\"When clicking \\u2018Test workflow\\u2019\": {\"main\": [[{\"node\": \"Pagination\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Code\", \"type\": \"main\", \"index\": 0}]]}, \"Pagination\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Loop Over Items\": {\"main\": [[], [{\"node\": \"HTTP Request\", \"type\": \"main\", \"index\": 0}]]}, \"Code\": {\"main\": [[{\"node\": \"Google Sheets\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"My workflow 8\", \"nodes\": [{\"parameters\": {\"path\": \"d1cba915-ca18-4425-bcfb-133205fc815a\", \"formTitle\": \"test\", \"formFields\": {\"values\": [{\"fieldLabel\": \"test\"}]}, \"options\": {}}, \"name\": \"n8n Form Trigger\", \"type\": \"n8n-nodes-base.formTrigger\", \"typeVersion\": 2, \"position\": [620, 580]}, {\"parameters\": {}, \"name\": \"Switch\", \"type\": \"n8n-nodes-base.switch\", \"typeVersion\": 2, \"position\": [800, 580]}], \"connections\": {\"n8n Form Trigger\": {\"main\": [[{\"node\": \"Switch\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"My workflow 8\", \"nodes\": [{\"parameters\": {\"path\": \"d1cba915-ca18-4425-bcfb-133205fc815a\", \"title\": \"test\", \"formFields\": {\"values\": [{\"fieldLabel\": \"test\"}]}, \"exampleItems\": [{\"name\": \"First item\", \"code\": 1}, {\"name\": \"Second item\", \"code\": 2}]}, \"name\": \"n8n Form Trigger\", \"type\": \"n8nFormTrigger\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {}, \"name\": \"Switch\", \"type\": \"switch\", \"typeVersion\": 1, \"position\": [650, 300]}], \"connections\": {\"n8n Form Trigger\": {\"main\": [[{\"node\": \"Switch\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"path\": \"0db0a40c-e5d1-463f-8252-03599f1303e6\", \"options\": {}, \"responseMode\": \"lastNode\"}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [460, 300]}, {\"parameters\": {\"type\": \"SHA256\", \"value\": \"={{$json[\\\"query\\\"][\\\"crc_token\\\"]}}\", \"action\": \"hmac\", \"secret\": \"API KEY SECRET\", \"encoding\": \"base64\"}, \"name\": \"Crypto\", \"type\": \"n8n-nodes-base.crypto\", \"typeVersion\": 1, \"position\": [660, 300]}, {\"parameters\": {\"values\": {\"string\": [{\"name\": \"response_token\", \"value\": \"=sha256={{$json[\\\"data\\\"]}}\"}]}, \"options\": {}, \"keepOnlySet\": true}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [840, 300]}], \"connections\": {\"Crypto\": {\"main\": [[{\"node\": \"Set\", \"type\": \"main\", \"index\": 0}]]}, \"Webhook\": {\"main\": [[{\"node\": \"Crypto\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"CRC Token Workflow\", \"nodes\": [{\"parameters\": {\"httpMethod\": \"GET\", \"path\": \"webhook\", \"queryParameters\": {\"parameters\": [{\"name\": \"crc_token\", \"value\": \"string\"}]}}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"operation\": \"hmac\", \"algorithm\": \"sha256\", \"secret\": \"API KEY SECRET\", \"data\": \"={{ $json.query.crc_token }}\", \"encoding\": \"base64\"}, \"name\": \"Crypto\", \"type\": \"n8n-nodes-base.crypto\", \"typeVersion\": 1, \"position\": [220, 0]}, {\"parameters\": {\"values\": {\"response_token\": \"=sha256={{ $json.hmac }}\"}}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [440, 0]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Crypto\", \"type\": \"main\", \"index\": 0}]]}, \"Crypto\": {\"main\": [[{\"node\": \"Set\", \"type\": \"main\", \"index\": 0}]]}, \"Set\": {\"main\": [[{\"node\": \"Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Agent:Tools:OpenAI\", \"nodes\": [{\"parameters\": {}, \"name\": \"Execute Workflow Trigger\", \"type\": \"n8n-nodes-base.executeWorkflowTrigger\", \"typeVersion\": 1, \"position\": [240, 980]}, {\"parameters\": {\"name\": \"get_weather_data\", \"description\": \"Call this tool to get weather information for a specific city. Input should be a single string in format: \\\"$CITY, $COUNTRY\\\". So for example to get data about Prague, \\\"Prague, Czechia\\\".\", \"workflowId\": \"={{ $workflow.id }}\", \"fields\": {\"values\": [{\"name\": \"tool\", \"stringValue\": \"get_weather\"}]}}, \"name\": \"Get Weather\", \"type\": \"@n8n/n8n-nodes-langchain.toolWorkflow\", \"typeVersion\": 1, \"position\": [640, 700]}, {\"parameters\": {\"name\": \"get_evens\", \"description\": \"Call this tool to get upcoming events for a specific city. Input should be a single string in format: \\\"$CITY, $COUNTRY\\\". So for example to get data about Prague, \\\"Prague, Czechia\\\".\", \"workflowId\": \"={{ $workflow.id }}\", \"fields\": {\"values\": [{\"name\": \"tool\", \"stringValue\": \"get_events\"}]}}, \"name\": \"Get Events\", \"type\": \"@n8n/n8n-nodes-langchain.toolWorkflow\", \"typeVersion\": 1, \"position\": [760, 700]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"fc61cf88-967d-4433-9cfd-7cdad1a43e75\", \"name\": \"response\", \"value\": \"={\\n    \\\"created\\\": \\\"2024-03-04T09:26:23+01:00\\\",\\n    \\\"symbolCode\\\": {\\n        \\\"next1Hour\\\": \\\"fog\\\"\\n    },\\n    \\\"temperature\\\": {\\n        \\\"value\\\": 5.1,\\n        \\\"feelsLike\\\": 4\\n    },\\n    \\\"precipitation\\\": {\\n        \\\"value\\\": 0.0\\n    },\\n    \\\"wind\\\": {\\n        \\\"direction\\\": 275,\\n        \\\"speed\\\": 1.7\\n    },\\n    \\\"status\\\": {\\n        \\\"code\\\": \\\"Ok\\\"\\n    }\\n}\", \"type\": \"string\"}]}, \"options\": {}}, \"name\": \"Edit Fields4\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [740, 920]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"0434695d-b245-4947-8b6e-7676a5c92904\", \"name\": \"response\", \"value\": \"=[\\n    {\\n        \\\"description\\\": \\\"***Movie Barf* is a new English friendly film night presented by film journalist and blogger Ryan Keating-Lambert, dedicated to screening a diverse variety of award-winning films both contemporary and classic. Ryan\\u2019s late night shows includes intriguing chats with various guests (in person or over Skype in the case of the international ones) and special drink offers at the bar.**\\\\n\\\\n*Dune: Part Two* / Denis Villeneuve / Canada, USA 2024 / 166 min \\u2013 Paul Atreides unites with Chani and the Fremen while seeking revenge against the conspirators who destroyed his family.\\\",\\n        \\\"name\\\": \\\"Movie Barf: Dune \\u2013 Part Two\\\",\\n        \\\"endDate\\\": \\\"Mon Mar 04 2024 23:59:59 GMT+0100 (Central European Standard Time)\\\"\\n    },\\n    {\\n        \\\"description\\\": \\\"Lubo\\u0161 Posp\\u00ed\\u0161il will perform with the renewed band 5P on March 14 at the cultural house of Barikadn\\u00edk.\\\",\\n        \\\"name\\\": \\\"Lubo\\u0161 Posp\\u00ed\\u0161il & 5P\\\",\\n        \\\"endDate\\\": \\\"Mon Mar 04 2024 23:59:59 GMT+0100 (Central European Standard Time)\\\"\\n    },\\n    {\\n        \\\"description\\\": \\\"An insomniac office worker looking for a way to change his life crosses paths with a devil-may-care soap maker and they form an underground fight club that evolves into something much, much more...\\\",\\n        \\\"name\\\": \\\"Fight Club\\\",\\n        \\\"endDate\\\": \\\"Mon Mar 04 2024 23:59:59 GMT+0100 (Central European Standard Time)\\\"\\n    },\\n    {\\n        \\\"description\\\": \\\"From filmmaker Yorgos Lanthimos and producer Emma Stone comes the incredible tale and fantastical evolution of Bella Baxter (Stone), a young woman brought back to life by the brilliant and unorthodox scientist Dr. Godwin Baxter (Willem Dafoe). Under Baxter's protection, Bella is eager to learn. Hungry for the worldliness she is lacking, she runs off with Duncan Wedderburn (Mark Ruffalo), a slick and debauched lawyer, on a whirlwind adventure across the continents. Free from the prejudices of her times, Bella grows steadfast in her purpose to stand for equality and liberation.\\\",\\n        \\\"name\\\": \\\"Poor Things\\\",\\n        \\\"endDate\\\": \\\"Mon Mar 04 2024 23:59:59 GMT+0100 (Central European Standard Time)\\\"\\n    },\\n    {\\n        \\\"description\\\": \\\"Concert of Bharata Rajno\\u0161ek, who decided to do something very brave - pay tribute to king of the pop, Michael Jackson in jazz.\\\",\\n        \\\"name\\\": \\\"Tribute to World Legends: Michael Jackson\\\",\\n        \\\"endDate\\\": \\\"Mon Mar 04 2024 23:59:59 GMT+0100 (Central European Standard Time)\\\"\\n    }\\n]\", \"type\": \"string\"}]}, \"options\": {}}, \"name\": \"Edit Fields5\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [740, 1100]}, {\"parameters\": {\"model\": \"gpt-4o-mini\", \"options\": {\"temperature\": 0}}, \"name\": \"OpenAI Chat Model4\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatOpenAi\", \"typeVersion\": 1, \"position\": [540, 540]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"414caf45-02aa-4c0a-9cdb-e6da9ec03d80\", \"name\": \"has_weather\", \"value\": \"={{ $json.output.includes('5.1') }}\", \"type\": \"boolean\"}, {\"id\": \"4f055fa4-10eb-4b7e-b1dc-37a7ef7185fc\", \"name\": \"has_movie\", \"value\": \"={{ $json.output.includes('Dune') }}\", \"type\": \"boolean\"}]}, \"options\": {}}, \"name\": \"Edit Fields6\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [900, 380]}, {\"parameters\": {}, \"name\": \"When clicking \\\"Test workflow\\\"\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [-40, -180]}, {\"parameters\": {\"content\": \"## Multiple Tools Calling\", \"height\": 80, \"width\": 335}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [500, 320]}, {\"parameters\": {\"content\": \"## Output Parsing\\n\", \"height\": 88, \"width\": 386}, \"name\": \"Sticky Note1\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [500, -240]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"0434695d-b245-4947-8b6e-7676a5c92904\", \"name\": \"response\", \"value\": \"=Maurits Cornelis Escher (Dutch pronunciation: [\\u02c8m\\u028cur\\u026ats k\\u0254r\\u02c8ne\\u02d0l\\u026as \\u02c8\\u025b\\u0255\\u0259r]; 17 June 1898 \\u2013 27 March 1972) was a Dutch graphic artist who made woodcuts, lithographs, and mezzotints, many of which were inspired by mathematics. Despite wide popular interest, for most of his life Escher was neglected in the art world, even in his native Netherlands. He was 70 before a retrospective exhibition was held. In the late twentieth century, he became more widely appreciated, and in the twenty-first century he has been celebrated in exhibitions around the world.\\n\\nHis work features mathematical objects and operations including impossible objects, explorations of infinity, reflection, symmetry, perspective, truncated and stellated polyhedra, hyperbolic geometry, and tessellations. Although Escher believed he had no mathematical ability, he interacted with the mathematicians George P\\u00f3lya, Roger Penrose, and Donald Coxeter, and the crystallographer Friedrich Haag, and conducted his own research into tessellation.\\n\\nEarly in his career, he drew inspiration from nature, making studies of insects, landscapes, and plants such as lichens, all of which he used as details in his artworks. He traveled in Italy and Spain, sketching buildings, townscapes, architecture and the tilings of the Alhambra and the Mezquita of Cordoba, and became steadily more interested in their mathematical structure.\\n\\nEscher's art became well known among scientists and mathematicians, and in popular culture, especially after it was featured by Martin Gardner in his April 1966 Mathematical Games column in Scientific American. Apart from being used in a variety of technical papers, his work has appeared on the covers of many books and albums. He was one of the major inspirations for Douglas Hofstadter's Pulitzer Prize-winning 1979 book G\\u00f6del, Escher, Bach.\\n\\nExhibitions\\n\\nPoster advertising the first major exhibition of Escher's work in Britain (Dulwich Picture Gallery, 14 October 2015 \\u2013 17 January 2016). The image, which shows Escher and his interest in geometric distortion and multiple levels of distance from reality, is based on his Hand with Reflecting Sphere, 1935.[62][22]\\nDespite wide popular interest, Escher was for a long time somewhat neglected in the art world; even in his native Netherlands, he was 70 before a retrospective exhibition was held.[43][k] In the twenty-first century, major exhibitions have been held in cities around the world.[63][64][65] An exhibition of his work in Rio de Janeiro attracted more than 573,000 visitors in 2011;[63] its daily visitor count of 9,677 made it the most visited museum exhibition of the year, anywhere in the world.[66] No major exhibition of Escher's work was held in Britain until 2015, when the Scottish National Gallery of Modern Art ran one in Edinburgh from June to September 2015,[64] moving in October 2015 to the Dulwich Picture Gallery, London. The exhibition poster is based on Hand with Reflecting Sphere, 1935, which shows Escher in his house reflected in a handheld sphere, thus illustrating the artist, his interest in levels of reality in art (e.g., is the hand in the foreground more real than the reflected one?), perspective, and spherical geometry.[22][62][67] The exhibition moved to Italy in 2015\\u20132016, attracting over 500,000 visitors in Rome and Bologna,[65] and then Milan.[68][69][70]\", \"type\": \"string\"}]}, \"options\": {}}, \"name\": \"Edit Fields\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [740, 1280]}, {\"parameters\": {\"rules\": {\"values\": [{\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"leftValue\": \"={{ $json.tool }}\", \"rightValue\": \"get_weather\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\"}}], \"combinator\": \"and\"}, \"renameOutput\": true, \"outputKey\": \"Weather\"}, {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"id\": \"a164188f-3b5b-4c24-b1bb-e589f4f9219f\", \"leftValue\": \"={{ $json.tool }}\", \"rightValue\": \"get_events\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\", \"name\": \"filter.operator.equals\"}}], \"combinator\": \"and\"}, \"renameOutput\": true, \"outputKey\": \"Events\"}, {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"id\": \"64f3dd1b-57e3-4183-a1d6-6b9b58fd1d81\", \"leftValue\": \"={{ $json.tool }}\", \"rightValue\": \"search_wiki\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\", \"name\": \"filter.operator.equals\"}}], \"combinator\": \"and\"}, \"renameOutput\": true, \"outputKey\": \"search_wiki\"}]}, \"options\": {}}, \"name\": \"Switch\", \"type\": \"n8n-nodes-base.switch\", \"typeVersion\": 3, \"position\": [460, 980]}, {\"parameters\": {\"name\": \"get_evens\", \"description\": \"Call this tool to search Wikipedia.\", \"workflowId\": \"={{ $workflow.id }}\", \"fields\": {\"values\": [{\"name\": \"tool\", \"stringValue\": \"search_wiki\"}]}}, \"name\": \"Search Wiki\", \"type\": \"@n8n/n8n-nodes-langchain.toolWorkflow\", \"typeVersion\": 1, \"position\": [660, 60]}, {\"parameters\": {\"model\": \"gpt-4o-mini\", \"options\": {\"temperature\": 0.1}}, \"name\": \"OpenAI Chat Model\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatOpenAi\", \"typeVersion\": 1, \"position\": [540, 0]}, {\"parameters\": {\"schemaType\": \"manual\", \"inputSchema\": \"{\\n  \\\"type\\\": \\\"object\\\",\\n  \\\"properties\\\": {\\n    \\\"name\\\": {\\n      \\\"type\\\": \\\"string\\\",\\n      \\\"description\\\": \\\"Full name of the artist\\\"\\n    },\\n    \\\"birthDate\\\": {\\n      \\\"type\\\": \\\"string\\\",\\n      \\\"format\\\": \\\"date\\\",\\n      \\\"description\\\": \\\"Date of birth\\\"\\n    },\\n    \\\"deathDate\\\": {\\n      \\\"type\\\": \\\"string\\\",\\n      \\\"format\\\": \\\"date\\\",\\n      \\\"description\\\": \\\"Date of death\\\"\\n    },\\n    \\\"nationality\\\": {\\n      \\\"type\\\": \\\"string\\\",\\n      \\\"description\\\": \\\"Artist's nationality\\\"\\n    },\\n    \\\"profession\\\": {\\n      \\\"type\\\": \\\"string\\\",\\n      \\\"description\\\": \\\"Artist's primary profession\\\"\\n    },\\n    \\\"notableWorks\\\": {\\n      \\\"type\\\": \\\"array\\\",\\n      \\\"items\\\": {\\n        \\\"type\\\": \\\"string\\\"\\n      },\\n      \\\"description\\\": \\\"Notable works created by the artist\\\"\\n    }\\n  },\\n  \\\"required\\\": [\\\"name\\\", \\\"birthDate\\\", \\\"deathDate\\\", \\\"nationality\\\", \\\"profession\\\", \\\"notableWorks\\\"]\\n}\"}, \"name\": \"Structured Output Parser\", \"type\": \"@n8n/n8n-nodes-langchain.outputParserStructured\", \"typeVersion\": 1.2, \"position\": [800, 0]}, {\"parameters\": {\"promptType\": \"define\", \"text\": \"Tell me about M.C. Escher\", \"hasOutputParser\": true, \"options\": {}}, \"name\": \"AI Agent\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.6, \"position\": [540, -180]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"414caf45-02aa-4c0a-9cdb-e6da9ec03d80\", \"name\": \"has_birth_date\", \"value\": \"={{ $json.output.birthDate === '1898-06-17' }}\", \"type\": \"boolean\"}, {\"id\": \"57513cf6-1ee7-40b7-95fc-316066c62153\", \"name\": \"has_death_date\", \"value\": \"={{ $json.output.deathDate === '1972-03-27' }}\", \"type\": \"string\"}, {\"id\": \"357953da-7578-4c7e-b8f8-aa25bd9187a4\", \"name\": \"has_name\", \"value\": \"={{ $json.output.name === 'Maurits Cornelis Escher' }}\", \"type\": \"string\"}, {\"id\": \"7cf215ea-f65a-467c-b158-9a80d8de7511\", \"name\": \"has_works\", \"value\": \"={{ $json.output.notableWorks.includes('Hand with Reflecting Sphere') }}\", \"type\": \"string\"}]}, \"options\": {}}, \"name\": \"Edit Fields7\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [900, -180]}, {\"parameters\": {\"content\": \"## Code Tool with Schema\\n\", \"height\": 88, \"width\": 386}, \"name\": \"Sticky Note2\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [500, -620]}, {\"parameters\": {\"name\": \"array_merge\", \"description\": \"Call this tool to merge array of strings to a single string\", \"jsCode\": \"// Example: convert the incoming query to uppercase and return it\\nreturn query.strings_array.join(', ');\", \"specifyInputSchema\": true, \"jsonSchemaExample\": \"{\\n\\t\\\"strings_array\\\": [\\\"some_value\\\", \\\"some_other_value\\\"]\\n}\"}, \"name\": \"Code Tool\", \"type\": \"@n8n/n8n-nodes-langchain.toolCode\", \"typeVersion\": 1.1, \"position\": [740, -400]}, {\"parameters\": {\"model\": \"gpt-4o-mini\", \"options\": {\"temperature\": 0.3}}, \"name\": \"OpenAI Chat Model1\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatOpenAi\", \"typeVersion\": 1, \"position\": [540, -420]}, {\"parameters\": {\"promptType\": \"define\", \"text\": \"Help me plan my day in Berlin, Germany. Check current the weather  and get the upcoming events and respond with weather and details about the upcoming events.\\n\\nEach tool should only be called only once.\", \"options\": {\"returnIntermediateSteps\": false}}, \"name\": \"AI Agent4\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.6, \"position\": [520, 380]}, {\"parameters\": {\"promptType\": \"define\", \"text\": \"Convert this JSON array to a single string: ['This', 'Is', 'An', 'Array!'].\", \"options\": {\"returnIntermediateSteps\": true}}, \"name\": \"AI Agent1\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.6, \"position\": [540, -560]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"414caf45-02aa-4c0a-9cdb-e6da9ec03d80\", \"name\": \"passed_array_parameter\", \"value\": \"={{ Array.isArray($json.intermediateSteps[0].action.messageLog[0].kwargs.tool_calls[0].args.strings_array) }}\", \"type\": \"boolean\"}, {\"id\": \"069c2fe9-f0f8-4938-9552-1dac5c720add\", \"name\": \"has_correct_length\", \"value\": \"={{ $json.intermediateSteps[0].action.messageLog[0].kwargs.tool_calls[0].args.strings_array.length === 4 }}\", \"type\": \"boolean\"}]}, \"options\": {}}, \"name\": \"Edit Fields8\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [900, -560]}, {\"parameters\": {\"content\": \"## Tool without parameters\\n\", \"height\": 88, \"width\": 386}, \"name\": \"Sticky Note3\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [560, -1020]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"414caf45-02aa-4c0a-9cdb-e6da9ec03d80\", \"name\": \"empty_args\", \"value\": \"={{ $ifEmpty($json.intermediateSteps[0].action.messageLog[0].kwargs.tool_calls[0].args, true) }}\", \"type\": \"boolean\"}]}, \"options\": {}}, \"name\": \"Edit Fields9\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [960, -960]}, {\"parameters\": {\"toolDescription\": \"Fetch Example website\", \"url\": \"https://example.com\"}, \"name\": \"HTTP Request\", \"type\": \"@n8n/n8n-nodes-langchain.toolHttpRequest\", \"typeVersion\": 1.1, \"position\": [800, -800]}, {\"parameters\": {\"model\": \"gpt-4o-mini\", \"options\": {\"temperature\": 0.3}}, \"name\": \"OpenAI Chat Model2\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatOpenAi\", \"typeVersion\": 1, \"position\": [600, -800]}, {\"parameters\": {\"promptType\": \"define\", \"text\": \"Fetch example website\", \"options\": {\"returnIntermediateSteps\": true}}, \"name\": \"AI Agent2\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.6, \"position\": [600, -960]}, {\"parameters\": {\"sessionIdType\": \"customKey\", \"sessionKey\": \"memory3\", \"contextWindowLength\": 10}, \"name\": \"Window Buffer Memory1\", \"type\": \"@n8n/n8n-nodes-langchain.memoryBufferWindow\", \"typeVersion\": 1.3, \"position\": [580, -1220]}, {\"parameters\": {\"jsonSchemaExample\": \"{\\n\\t\\\"english_answer\\\": \\\"California\\\",\\n\\t\\\"czech_answer\\\": \\\"California\\\"\\n}\"}, \"name\": \"Structured Output Parser1\", \"type\": \"@n8n/n8n-nodes-langchain.outputParserStructured\", \"typeVersion\": 1.2, \"position\": [720, -1220]}, {\"parameters\": {\"model\": \"gpt-4o-mini\", \"options\": {\"temperature\": 0.1}}, \"name\": \"OpenAI Chat Model3\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatOpenAi\", \"typeVersion\": 1, \"position\": [440, -1220]}, {\"parameters\": {\"promptType\": \"define\", \"text\": \"Can you still remember my name?\", \"hasOutputParser\": true, \"options\": {\"systemMessage\": \"You are a helpful assistant. Always provide both `english_answer` and `czech_answer` in the final output and be very concise.\"}}, \"name\": \"AI Agent3\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.6, \"position\": [960, -1380]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"492acf62-f7d5-4798-a93c-6a291421ecfb\", \"name\": \"contain_both_answers\", \"value\": \"={{ $json.output.english_answer.length > 0 && $json.output.czech_answer.length > 0 }}\", \"type\": \"boolean\"}, {\"id\": \"5c56b6d3-1d59-45c4-bcb8-3d4722493c62\", \"name\": \"recalled_name\", \"value\": \"={{ $json.output.english_answer.includes('Oleg') }}\", \"type\": \"string\"}]}, \"options\": {}}, \"name\": \"Edit Fields1\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [1300, -1380]}, {\"parameters\": {\"promptType\": \"define\", \"text\": \"Hi, my name is Oleg. Tell me about magnets like I'm 5.\", \"hasOutputParser\": true, \"options\": {\"systemMessage\": \"You are a helpful assistant. Always provide both `english_answer` and `czech_answer` in the final output and be very concise.\"}}, \"name\": \"AI Agent5\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.6, \"position\": [600, -1380]}, {\"parameters\": {\"content\": \"## Output Parser + Memory\\n\", \"height\": 88, \"width\": 386}, \"name\": \"Sticky Note4\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [580, -1440]}], \"connections\": {\"Execute Workflow Trigger\": {\"main\": [[{\"node\": \"Switch\", \"type\": \"main\", \"index\": 0}]]}, \"Get Weather\": {\"ai_tool\": [[{\"node\": \"AI Agent4\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"Get Events\": {\"ai_tool\": [[{\"node\": \"AI Agent4\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"OpenAI Chat Model4\": {\"ai_languageModel\": [[{\"node\": \"AI Agent4\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"Switch\": {\"main\": [[{\"node\": \"Edit Fields4\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Edit Fields5\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Edit Fields\", \"type\": \"main\", \"index\": 0}]]}, \"Search Wiki\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"OpenAI Chat Model\": {\"ai_languageModel\": [[{\"node\": \"AI Agent\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"Structured Output Parser\": {\"ai_outputParser\": [[{\"node\": \"AI Agent\", \"type\": \"ai_outputParser\", \"index\": 0}]]}, \"AI Agent\": {\"main\": [[{\"node\": \"Edit Fields7\", \"type\": \"main\", \"index\": 0}]]}, \"Code Tool\": {\"ai_tool\": [[{\"node\": \"AI Agent1\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"OpenAI Chat Model1\": {\"ai_languageModel\": [[{\"node\": \"AI Agent1\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"When clicking \\\"Test workflow\\\"\": {\"main\": [[{\"node\": \"AI Agent1\", \"type\": \"main\", \"index\": 0}, {\"node\": \"AI Agent4\", \"type\": \"main\", \"index\": 0}, {\"node\": \"AI Agent\", \"type\": \"main\", \"index\": 0}, {\"node\": \"AI Agent2\", \"type\": \"main\", \"index\": 0}, {\"node\": \"AI Agent5\", \"type\": \"main\", \"index\": 0}]]}, \"AI Agent4\": {\"main\": [[{\"node\": \"Edit Fields6\", \"type\": \"main\", \"index\": 0}]]}, \"AI Agent1\": {\"main\": [[{\"node\": \"Edit Fields8\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request\": {\"ai_tool\": [[{\"node\": \"AI Agent2\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"OpenAI Chat Model2\": {\"ai_languageModel\": [[{\"node\": \"AI Agent2\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"AI Agent2\": {\"main\": [[{\"node\": \"Edit Fields9\", \"type\": \"main\", \"index\": 0}]]}, \"Window Buffer Memory1\": {\"ai_memory\": [[{\"node\": \"AI Agent5\", \"type\": \"ai_memory\", \"index\": 0}, {\"node\": \"AI Agent3\", \"type\": \"ai_memory\", \"index\": 0}]]}, \"Structured Output Parser1\": {\"ai_outputParser\": [[{\"node\": \"AI Agent5\", \"type\": \"ai_outputParser\", \"index\": 0}, {\"node\": \"AI Agent3\", \"type\": \"ai_outputParser\", \"index\": 0}]]}, \"OpenAI Chat Model3\": {\"ai_languageModel\": [[{\"node\": \"AI Agent5\", \"type\": \"ai_languageModel\", \"index\": 0}, {\"node\": \"AI Agent3\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"AI Agent3\": {\"main\": [[{\"node\": \"Edit Fields1\", \"type\": \"main\", \"index\": 0}]]}, \"AI Agent5\": {\"main\": [[{\"node\": \"AI Agent3\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"<PERSON>rape and summarize webpages with AI\", \"nodes\": [{\"parameters\": {}, \"name\": \"When clicking \\\"Execute Workflow\\\"\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [-1960, 160]}, {\"parameters\": {\"content\": \"## Scrape latest <PERSON> essays\", \"height\": 285.66037735849045, \"width\": 1071.752021563343}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-1760, 40]}, {\"parameters\": {\"content\": \"## Summarize them with GPT\", \"height\": 607, \"width\": 625}, \"name\": \"Sticky Note1\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-640, 40]}, {\"parameters\": {\"url\": \"http://www.paulgraham.com/articles.html\", \"options\": {}}, \"name\": \"Fetch Essay List\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [-1680, 160]}, {\"parameters\": {\"model\": {\"__rl\": true, \"mode\": \"list\", \"value\": \"gpt-4o-mini\"}, \"options\": {}}, \"name\": \"OpenAI Chat Model\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatOpenAi\", \"typeVersion\": 1.2, \"position\": [-420, 380]}, {\"parameters\": {\"operation\": \"extractHtmlContent\", \"extractionValues\": {\"values\": [{\"key\": \"essay\", \"cssSelector\": \"table table a\", \"returnValue\": \"attribute\", \"attribute\": \"href\", \"returnArray\": true}]}, \"options\": {}}, \"name\": \"Extract essay names\", \"type\": \"n8n-nodes-base.html\", \"typeVersion\": 1.2, \"position\": [-1480, 160]}, {\"parameters\": {\"fieldToSplitOut\": \"essay\", \"options\": {}}, \"name\": \"Split out into items\", \"type\": \"n8n-nodes-base.splitOut\", \"typeVersion\": 1, \"position\": [-1280, 160]}, {\"parameters\": {\"url\": \"=http://www.paulgraham.com/{{ $json.essay }}\", \"options\": {}}, \"name\": \"Fetch essay texts\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [-880, 160]}, {\"parameters\": {\"operation\": \"extractHtmlContent\", \"extractionValues\": {\"values\": [{\"key\": \"title\", \"cssSelector\": \"title\"}]}, \"options\": {}}, \"name\": \"Extract title\", \"type\": \"n8n-nodes-base.html\", \"typeVersion\": 1.2, \"position\": [-380, -140]}, {\"parameters\": {\"operationMode\": \"documentLoader\", \"options\": {}}, \"name\": \"Summarization Chain\", \"type\": \"@n8n/n8n-nodes-langchain.chainSummarization\", \"typeVersion\": 2, \"position\": [-380, 160]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"7b337b47-a1c6-470e-881f-0c038b4917e5\", \"name\": \"title\", \"type\": \"string\", \"value\": \"={{ $json.title }}\"}, {\"id\": \"ca820521-4fff-4971-84b5-e6e2dbd8bb7a\", \"name\": \"summary\", \"type\": \"string\", \"value\": \"={{ $json.response.text }}\"}, {\"id\": \"0fd9b5e3-44dd-49a3-82c1-3a4aa4698376\", \"name\": \"url\", \"type\": \"string\", \"value\": \"=http://www.paulgraham.com/{{ $('Limit to first 3').first().json.essay }}\"}]}, \"options\": {}}, \"name\": \"Clean up\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [320, 160]}, {\"parameters\": {\"mode\": \"combine\", \"combineBy\": \"combineByPosition\", \"options\": {}}, \"name\": \"Merge\", \"type\": \"n8n-nodes-base.merge\", \"typeVersion\": 3, \"position\": [120, 160]}, {\"parameters\": {\"jsonMode\": \"expressionData\", \"jsonData\": \"={{ $('Extract Text Only').item.json.data }}\", \"options\": {}}, \"name\": \"Default Data Loader\", \"type\": \"@n8n/n8n-nodes-langchain.documentDefaultDataLoader\", \"typeVersion\": 1, \"position\": [-300, 380]}, {\"parameters\": {\"chunkSize\": 6000, \"options\": {}}, \"name\": \"Recursive Character Text Splitter\", \"type\": \"@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter\", \"typeVersion\": 1, \"position\": [-220, 500]}, {\"parameters\": {\"maxItems\": 3}, \"name\": \"Limit to first 3\", \"type\": \"n8n-nodes-base.limit\", \"typeVersion\": 1, \"position\": [-1080, 160]}, {\"parameters\": {\"operation\": \"extractHtmlContent\", \"extractionValues\": {\"values\": [{\"key\": \"data\", \"cssSelector\": \"body\", \"skipSelectors\": \"img,nav\"}]}, \"options\": {}}, \"name\": \"Extract Text Only\", \"type\": \"n8n-nodes-base.html\", \"typeVersion\": 1.2, \"position\": [-560, 160]}], \"connections\": {\"When clicking \\\"Execute Workflow\\\"\": {\"main\": [[{\"node\": \"Fetch Essay List\", \"type\": \"main\", \"index\": 0}]]}, \"Fetch Essay List\": {\"main\": [[{\"node\": \"Extract essay names\", \"type\": \"main\", \"index\": 0}]]}, \"OpenAI Chat Model\": {\"ai_languageModel\": [[{\"node\": \"Summarization Chain\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"Extract essay names\": {\"main\": [[{\"node\": \"Split out into items\", \"type\": \"main\", \"index\": 0}]]}, \"Split out into items\": {\"main\": [[{\"node\": \"Limit to first 3\", \"type\": \"main\", \"index\": 0}]]}, \"Fetch essay texts\": {\"main\": [[{\"node\": \"Extract title\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Extract Text Only\", \"type\": \"main\", \"index\": 0}]]}, \"Extract title\": {\"main\": [[{\"node\": \"Merge\", \"type\": \"main\", \"index\": 0}]]}, \"Summarization Chain\": {\"main\": [[{\"node\": \"Merge\", \"type\": \"main\", \"index\": 1}]]}, \"Merge\": {\"main\": [[{\"node\": \"Clean up\", \"type\": \"main\", \"index\": 0}]]}, \"Default Data Loader\": {\"ai_document\": [[{\"node\": \"Summarization Chain\", \"type\": \"ai_document\", \"index\": 0}]]}, \"Recursive Character Text Splitter\": {\"ai_textSplitter\": [[{\"node\": \"Default Data Loader\", \"type\": \"ai_textSplitter\", \"index\": 0}]]}, \"Limit to first 3\": {\"main\": [[{\"node\": \"Fetch essay texts\", \"type\": \"main\", \"index\": 0}]]}, \"Extract Text Only\": {\"main\": [[{\"node\": \"Summarization Chain\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"<PERSON><PERSON><PERSON> and <PERSON>mmarize <PERSON>'s Essays\", \"nodes\": [{\"parameters\": {\"url\": \"https://www.paulgraham.com/essays.html\", \"responseFormat\": \"json\"}, \"name\": \"HTTP Request\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [0, 0]}, {\"parameters\": {\"xpath\": \"//a[contains(@class, 'postlink')]/@href\"}, \"name\": \"XPath\", \"type\": \"n8n-nodes-base.xpath\", \"typeVersion\": 1, \"position\": [220, 0]}, {\"parameters\": {\"maxItems\": 3}, \"name\": \"Limit\", \"type\": \"n8n-nodes-base.limit\", \"typeVersion\": 1, \"position\": [440, 0]}, {\"parameters\": {\"url\": \"={{$node[\\\"XPath\\\"].data}}\", \"responseFormat\": \"json\"}, \"name\": \"HTTP Request1\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [660, 0]}, {\"parameters\": {\"text\": \"={{$node[\\\"HTTP Request\\\"].data}}\"}, \"name\": \"Summarization Chain\", \"type\": \"n8n-nodes-base.summarizationChain\", \"typeVersion\": 1, \"position\": [880, 0]}, {\"parameters\": {\"model\": \"gpt-3.5-turbo\", \"temperature\": 0}, \"name\": \"OpenAI\", \"type\": \"n8n-nodes-base.openAi\", \"typeVersion\": 1, \"position\": [1100, 0]}, {\"parameters\": {\"fields\": {\"title\": \"={{$node[\\\"XPath\\\"].data[0].text}}\", \"summary\": \"={{$node[\\\"Summarization Chain\\\"].data}}\", \"url\": \"={{$node[\\\"XPath\\\"].data[0].href}}\"}, \"keepOnlySet\": true}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [1320, 0]}, {\"parameters\": {\"content\": \"## Scrape and Summarize Paul Graham's Essays\\nThis workflow scrapes the latest Paul Graham essays from his website, limits the process to the first three essays, and then fetches their full content. For each essay, it uses an AI summarization chain with an OpenAI model to generate a concise summary, and finally outputs the essay's title, the generated summary, and its original URL.\"}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [0, 200]}], \"connections\": {\"HTTP Request\": {\"main\": [[{\"node\": \"XPath\", \"type\": \"main\", \"index\": 0}]]}, \"XPath\": {\"main\": [[{\"node\": \"Limit\", \"type\": \"main\", \"index\": 0}]]}, \"Limit\": {\"main\": [[{\"node\": \"HTTP Request1\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request1\": {\"main\": [[{\"node\": \"Summarization Chain\", \"type\": \"main\", \"index\": 0}]]}, \"Summarization Chain\": {\"main\": [[{\"node\": \"Set\", \"type\": \"main\", \"index\": 0}]]}, \"OpenAI\": {\"ai_languageModel\": [[{\"node\": \"Summarization Chain\", \"type\": \"ai_languageModel\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"My workflow\", \"nodes\": [{\"parameters\": {\"updates\": [\"message\"], \"additionalFields\": {}}, \"name\": \"Telegram Trigger1\", \"type\": \"n8n-nodes-base.telegramTrigger\", \"typeVersion\": 1.1, \"position\": [-100, 640]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"ead608af-7a53-4330-a679-6a5df4bd5ad8\", \"name\": \"text\", \"value\": \"={{ $json.message.text }}\", \"type\": \"string\"}]}, \"options\": {}}, \"name\": \"Edit Fields1\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [360, 660]}, {\"parameters\": {\"operation\": \"update\", \"calendar\": {\"__rl\": true, \"value\": \"<EMAIL>\", \"mode\": \"id\"}, \"eventId\": \"={{ $fromAI(\\\"Event_ID\\\",\\\"it is the id of the specific event\\\") }}\", \"updateFields\": {\"end\": \"={{ $fromAI(\\\"end\\\",\\\"it is the meeting date ending time\\\") }}\", \"start\": \"={{ $fromAI(\\\"Start\\\",\\\"it is the meeting date starting time\\\") }}\", \"summary\": \"={{ $fromAI(\\\"eventName\\\",\\\"the name of the calendar event\\\") }}\"}}, \"name\": \"Update calendar\", \"type\": \"n8n-nodes-base.googleCalendarTool\", \"typeVersion\": 1.3, \"position\": [1060, 940]}, {\"parameters\": {\"descriptionType\": \"manual\", \"toolDescription\": \"Use this tool to create a new calendar event.\", \"calendar\": {\"__rl\": true, \"value\": \"<EMAIL>\", \"mode\": \"id\"}, \"start\": \"={{ $fromAI(\\\"Start\\\",\\\"it is the meeting date starting time\\\") }}\", \"end\": \"={{ $fromAI(\\\"end\\\",\\\"it is the meeting date ending time\\\") }}\", \"additionalFields\": {\"summary\": \"={{ $fromAI(\\\"eventName\\\",\\\"the name of the calendar event\\\") }}\"}}, \"name\": \"Create event\", \"type\": \"n8n-nodes-base.googleCalendarTool\", \"typeVersion\": 1.3, \"position\": [680, 1100]}, {\"parameters\": {\"operation\": \"delete\", \"calendar\": {\"__rl\": true, \"value\": \"<EMAIL>\", \"mode\": \"id\"}, \"eventId\": \"={{ $fromAI(\\\"Event_ID\\\",\\\"it is the id of the specific event\\\") }}\", \"options\": {}}, \"name\": \"Delete event\", \"type\": \"n8n-nodes-base.googleCalendarTool\", \"typeVersion\": 1.3, \"position\": [820, 1100]}, {\"parameters\": {\"operation\": \"getAll\", \"calendar\": {\"__rl\": true, \"value\": \"<EMAIL>\", \"mode\": \"id\"}, \"returnAll\": true, \"timeMin\": \"={{ $fromAI('After', ``, 'string') }}\", \"timeMax\": \"={{ $now.plus({ week: 4 }) }}\", \"options\": {}}, \"name\": \"GetAll events\", \"type\": \"n8n-nodes-base.googleCalendarTool\", \"typeVersion\": 1.3, \"position\": [940, 1020]}, {\"parameters\": {\"rules\": {\"values\": [{\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\", \"version\": 2}, \"conditions\": [{\"id\": \"502e50b6-9903-44c0-9a0d-a3bc772eae71\", \"leftValue\": \"={{ $json.message.voice.file_id }}\", \"rightValue\": \"\", \"operator\": {\"type\": \"string\", \"operation\": \"exists\", \"singleValue\": true}}], \"combinator\": \"and\"}, \"renameOutput\": true, \"outputKey\": \"Audio\"}, {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\", \"version\": 2}, \"conditions\": [{\"id\": \"442d61ad-3a99-4d7a-93cf-753a7c877460\", \"leftValue\": \"={{ $json.message.text || \\\"\\\" }}\", \"rightValue\": \"\", \"operator\": {\"type\": \"string\", \"operation\": \"exists\", \"singleValue\": true}}], \"combinator\": \"and\"}, \"renameOutput\": true, \"outputKey\": \"Text\"}]}, \"options\": {}}, \"name\": \"Text or Audio\", \"type\": \"n8n-nodes-base.switch\", \"typeVersion\": 3.2, \"position\": [80, 640]}, {\"parameters\": {\"sessionIdType\": \"customKey\", \"sessionKey\": \"={{ $('Telegram Trigger1').item.json.update_id }}\"}, \"name\": \"Window Buffer Memory\", \"type\": \"@n8n/n8n-nodes-langchain.memoryBufferWindow\", \"typeVersion\": 1.3, \"position\": [560, 1020]}, {\"parameters\": {\"promptType\": \"define\", \"text\": \"={{ $json.text }}\", \"options\": {\"systemMessage\": \"=You are a helpful assistant and answer in the language that the person talks to you. The current date is{{ $now }}\\n\\nYou use the LLM when nothing is asked about the calendar. When asked to create an event, use the create event tool. When an event has to be deleted, use the delete tool. When asked about an event or a specific date, use the get tool. When something needs to be updated, use the update tool.\"}}, \"name\": \"AI Agent\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.7, \"position\": [620, 660]}, {\"parameters\": {\"chatId\": \"={{ $('Telegram Trigger1').item.json.message.chat.id }}\", \"text\": \"={{ $json.output }}\", \"additionalFields\": {\"appendAttribution\": false}}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [980, 660]}, {\"parameters\": {\"model\": {\"__rl\": true, \"mode\": \"list\", \"value\": \"gpt-4o-mini\"}, \"options\": {}}, \"name\": \"OpenAI\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatOpenAi\", \"typeVersion\": 1.2, \"position\": [440, 940]}], \"connections\": {\"Telegram Trigger1\": {\"main\": [[{\"node\": \"Text or Audio\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields1\": {\"main\": [[{\"node\": \"AI Agent\", \"type\": \"main\", \"index\": 0}]]}, \"Update calendar\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"Create event\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"Delete event\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"GetAll events\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"Text or Audio\": {\"main\": [[], [{\"node\": \"Edit Fields1\", \"type\": \"main\", \"index\": 0}]]}, \"Window Buffer Memory\": {\"ai_memory\": [[{\"node\": \"AI Agent\", \"type\": \"ai_memory\", \"index\": 0}]]}, \"AI Agent\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}, \"OpenAI\": {\"ai_languageModel\": [[{\"node\": \"AI Agent\", \"type\": \"ai_languageModel\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"Verify phone numbers\", \"nodes\": [{\"parameters\": {}, \"name\": \"On clicking 'execute'\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [440, 510]}, {\"parameters\": {\"functionCode\": \"item.phone = \\\"+34605281220\\\";\\nreturn item;\"}, \"name\": \"Create Phone Item\", \"type\": \"n8n-nodes-base.functionItem\", \"typeVersion\": 1, \"position\": [640, 510]}, {\"parameters\": {\"tool\": \"getPhoneParsed\", \"phone\": \"={{$node[\\\"Create Phone Item\\\"].json[\\\"phone\\\"]}}\", \"additionalOptions\": {}}, \"name\": \"Parse and Validate Phone\", \"type\": \"n8n-nodes-base.uproc\", \"typeVersion\": 1, \"position\": [850, 510]}, {\"parameters\": {\"conditions\": {\"string\": [{\"value1\": \"={{$node[\\\"Parse and Validate Phone\\\"].json[\\\"message\\\"][\\\"valid\\\"]+\\\"\\\"}}\", \"value2\": \"true\"}]}}, \"name\": \"Phone is Valid?\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 1, \"position\": [1050, 510]}], \"connections\": {\"Create Phone Item\": {\"main\": [[{\"node\": \"Parse and Validate Phone\", \"type\": \"main\", \"index\": 0}]]}, \"On clicking 'execute'\": {\"main\": [[{\"node\": \"Create Phone Item\", \"type\": \"main\", \"index\": 0}]]}, \"Parse and Validate Phone\": {\"main\": [[{\"node\": \"Phone is Valid?\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Check phone validity\", \"nodes\": [{\"parameters\": {\"phone\": \"+34605281220\"}, \"name\": \"Set Phone\", \"type\": \"set\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"tool\": \"getPhoneParsed\", \"credentials\": {\"uproc\": \"miquel-uproc\"}}, \"name\": \"Parse Phone\", \"type\": \"uproc\", \"typeVersion\": 1, \"position\": [650, 300]}, {\"parameters\": {\"conditions\": {\"boolean\": [{\"value1\": \"={{ $json.valid }}\", \"value2\": true}]}}, \"name\": \"Check Validity\", \"type\": \"If\", \"typeVersion\": 1, \"position\": [850, 300]}, {\"parameters\": {}, \"name\": \"Manual Trigger\", \"type\": \"manualTrigger\", \"typeVersion\": 1, \"position\": [250, 300]}], \"connections\": {\"Manual Trigger\": {\"main\": [[{\"node\": \"Set Phone\", \"type\": \"main\", \"index\": 0}]]}, \"Set Phone\": {\"main\": [[{\"node\": \"Parse Phone\", \"type\": \"main\", \"index\": 0}]]}, \"Parse Phone\": {\"main\": [[{\"node\": \"Check Validity\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {}, \"name\": \"On clicking 'execute'\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [210, 300]}, {\"parameters\": {\"url\": \"\", \"version\": \"0.0.1\", \"projects\": [\"\"], \"resource\": \"release\", \"operation\": \"create\", \"additionalFields\": {}, \"organizationSlug\": \"\"}, \"name\": \"Sentry.io\", \"type\": \"n8n-nodes-base.sentryIo\", \"typeVersion\": 1, \"position\": [410, 300]}, {\"parameters\": {\"resource\": \"release\", \"operation\": \"getAll\", \"returnAll\": true, \"additionalFields\": {}, \"organizationSlug\": \"\"}, \"name\": \"Sentry.io1\", \"type\": \"n8n-nodes-base.sentryIo\", \"typeVersion\": 1, \"position\": [610, 300]}], \"connections\": {\"Sentry.io\": {\"main\": [[{\"node\": \"Sentry.io1\", \"type\": \"main\", \"index\": 0}]]}, \"On clicking 'execute'\": {\"main\": [[{\"node\": \"Sentry.io\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Create and get all releases\", \"nodes\": [{\"parameters\": {\"name\": \"test\", \"operation\": \"create release\"}, \"name\": \"Create release\", \"type\": \"n8n-nodes-base.sentry\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {}, \"name\": \"Manual Trigger\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [250, 300]}, {\"parameters\": {\"operation\": \"get all releases\"}, \"name\": \"Get all releases\", \"type\": \"n8n-nodes-base.sentry\", \"typeVersion\": 1, \"position\": [650, 300]}], \"connections\": {\"Create release\": {\"main\": [[{\"node\": \"Get all releases\", \"type\": \"main\", \"index\": 0}]]}, \"Manual Trigger\": {\"main\": [[{\"node\": \"Create release\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Tutorial n8n - nocodb\", \"nodes\": [{\"parameters\": {}, \"name\": \"When clicking \\u2018Test workflow\\u2019\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [680, 400]}, {\"parameters\": {\"authentication\": \"nocoDbApiToken\", \"operation\": \"getAll\", \"projectId\": \"pcdrdmidko5oz5y\", \"table\": \"idtable\", \"returnAll\": true, \"options\": {\"where\": \"(estado,eq,dormido)\"}}, \"name\": \"NocoDB\", \"type\": \"n8n-nodes-base.nocoDb\", \"typeVersion\": 3, \"position\": [900, 400]}, {\"parameters\": {\"authentication\": \"nocoDbApiToken\", \"operation\": \"update\", \"projectId\": \"idproyecto\", \"table\": \"gguidtable\", \"fieldsUi\": {\"fieldValues\": [{\"fieldName\": \"Id\", \"fieldValue\": \"={{ $json.Id }}\"}, {\"fieldName\": \"texto\", \"fieldValue\": \"es una prueba\"}]}}, \"name\": \"NocoDB1\", \"type\": \"n8n-nodes-base.nocoDb\", \"typeVersion\": 3, \"position\": [1120, 400]}], \"connections\": {\"When clicking \\u2018Test workflow\\u2019\": {\"main\": [[{\"node\": \"NocoDB\", \"type\": \"main\", \"index\": 0}]]}, \"NocoDB\": {\"main\": [[{\"node\": \"NocoDB1\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Tutorial n8n - nocodb\", \"nodes\": [{\"parameters\": {\"table\": \"idtable\", \"project\": \"pcdrdmidko5oz5y\", \"where\": \"estado=dormido\"}, \"name\": \"NocoDB\", \"type\": \"n8n-nodes-base.nocoDb\", \"typeVersion\": 1, \"position\": [500, 300]}, {\"parameters\": {\"table\": \"gguidtable\", \"project\": \"idproyecto\", \"operation\": \"update\", \"updateFields\": {\"texto\": \"es una prueba\", \"Id\": \"={{ $json.Id }}\"}}, \"name\": \"NocoDB1\", \"type\": \"n8n-nodes-base.nocoDb\", \"typeVersion\": 1, \"position\": [700, 300]}, {\"parameters\": {}, \"name\": \"Manual Trigger\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [300, 300]}], \"connections\": {\"Manual Trigger\": {\"main\": [[{\"node\": \"NocoDB\", \"type\": \"main\", \"index\": 0}]]}, \"NocoDB\": {\"main\": [[{\"node\": \"NocoDB1\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"MySQL:insert update executeQuery\", \"nodes\": [{\"parameters\": {}, \"name\": \"Start\", \"type\": \"n8n-nodes-base.start\", \"typeVersion\": 1, \"position\": [250, 300]}, {\"parameters\": {\"values\": {\"string\": [{\"name\": \"name\", \"value\": \"=Name{{Date.now()}}\"}], \"number\": [{\"name\": \"id\", \"value\": \"={{Math.round(Math.random()*100000)}}\"}]}, \"options\": {}}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"values\": {\"string\": [{\"name\": \"name\", \"value\": \"=UpdatedName{{Date.now()}}\"}], \"number\": [{\"name\": \"id\", \"value\": \"={{$node[\\\"Set\\\"].json[\\\"id\\\"]}}\"}]}, \"options\": {}}, \"name\": \"Set1\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [850, 300]}, {\"parameters\": {\"table\": \"TestTable\", \"columns\": \"id,name\"}, \"name\": \"MySQL\", \"type\": \"n8n-nodes-base.mySql\", \"typeVersion\": 1, \"position\": [650, 300]}, {\"parameters\": {\"operation\": \"update\", \"table\": \"TestTable\", \"columns\": \"id,name\"}, \"name\": \"MySQL1\", \"type\": \"n8n-nodes-base.mySql\", \"typeVersion\": 1, \"position\": [1050, 300]}, {\"parameters\": {\"operation\": \"executeQuery\", \"query\": \"=SELECT * FROM TestTable LIMIT 1;\"}, \"name\": \"MySQL2\", \"type\": \"n8n-nodes-base.mySql\", \"typeVersion\": 1, \"position\": [1250, 300]}, {\"parameters\": {\"operation\": \"executeQuery\", \"query\": \"=DELETE FROM TestTable WHERE id={{$node[\\\"Set1\\\"].json[\\\"id\\\"]}};\"}, \"name\": \"MySQL3\", \"type\": \"n8n-nodes-base.mySql\", \"typeVersion\": 1, \"position\": [1450, 300]}], \"connections\": {\"Set\": {\"main\": [[{\"node\": \"MySQL\", \"type\": \"main\", \"index\": 0}]]}, \"Set1\": {\"main\": [[{\"node\": \"MySQL1\", \"type\": \"main\", \"index\": 0}]]}, \"Start\": {\"main\": [[{\"node\": \"Set\", \"type\": \"main\", \"index\": 0}]]}, \"MySQL\": {\"main\": [[{\"node\": \"Set1\", \"type\": \"main\", \"index\": 0}]]}, \"MySQL1\": {\"main\": [[{\"node\": \"MySQL2\", \"type\": \"main\", \"index\": 0}]]}, \"MySQL2\": {\"main\": [[{\"node\": \"MySQL3\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"MySQL\", \"nodes\": [{\"parameters\": {\"id\": 1, \"name\": \"Test\"}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [250, 300]}, {\"parameters\": {\"table\": \"TestTable\", \"columns\": \"id, name\", \"operation\": \"insert\"}, \"name\": \"MySQL\", \"type\": \"n8n-nodes-base.mySql\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"id\": \"={{$node[\\\"MySQL\\\"].data[0][\\\"id\\\"]}}\"}, \"name\": \"Set1\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [650, 300]}, {\"parameters\": {\"table\": \"TestTable\", \"columns\": \"id, name\", \"operation\": \"update\"}, \"name\": \"MySQL1\", \"type\": \"n8n-nodes-base.mySql\", \"typeVersion\": 1, \"position\": [850, 300]}, {\"parameters\": {\"table\": \"TestTable\", \"columns\": \"id, name\", \"operation\": \"select\"}, \"name\": \"MySQL2\", \"type\": \"n8n-nodes-base.mySql\", \"typeVersion\": 1, \"position\": [1050, 300]}, {\"parameters\": {\"table\": \"TestTable\", \"columns\": \"id\", \"operation\": \"delete\"}, \"name\": \"MySQL3\", \"type\": \"n8n-nodes-base.mySql\", \"typeVersion\": 1, \"position\": [1250, 300]}], \"connections\": {\"Set\": {\"main\": [[{\"node\": \"MySQL\", \"type\": \"main\", \"index\": 0}]]}, \"MySQL\": {\"main\": [[{\"node\": \"Set1\", \"type\": \"main\", \"index\": 0}]]}, \"Set1\": {\"main\": [[{\"node\": \"MySQL1\", \"type\": \"main\", \"index\": 0}]]}, \"MySQL1\": {\"main\": [[{\"node\": \"MySQL2\", \"type\": \"main\", \"index\": 0}]]}, \"MySQL2\": {\"main\": [[{\"node\": \"MySQL3\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Send a message on Mattermost when an order is created in WooCommerce\", \"nodes\": [{\"parameters\": {\"event\": \"order.created\"}, \"name\": \"WooCommerce Trigger\", \"type\": \"n8n-nodes-base.wooCommerceTrigger\", \"typeVersion\": 1, \"position\": [550, 260]}, {\"parameters\": {\"message\": \"={{$node[\\\"WooCommerce Trigger\\\"].json[\\\"billing\\\"][\\\"first_name\\\"]}} bought {{$node[\\\"WooCommerce Trigger\\\"].json[\\\"line_items\\\"][0][\\\"name\\\"]}}!\", \"channelId\": \"pj1p95ebei8g3ro5p84kxxuuio\", \"attachments\": [], \"otherOptions\": {}}, \"name\": \"Mattermost\", \"type\": \"n8n-nodes-base.mattermost\", \"typeVersion\": 1, \"position\": [750, 260]}], \"connections\": {\"WooCommerce Trigger\": {\"main\": [[{\"node\": \"Mattermost\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"WooCommerce Order to Mattermost\", \"nodes\": [{\"parameters\": {\"event\": \"order.created\"}, \"name\": \"WooCommerce Trigger\", \"type\": \"n8n-nodes-base.wooCommerceTrigger\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"message\": \"={{$node[\\\"WooCommerce Trigger\\\"].json[\\\"billing\\\"][\\\"first_name\\\"]}} bought {{$node[\\\"WooCommerce Trigger\\\"].json[\\\"line_items\\\"][0][\\\"name\\\"]}}!\", \"channelId\": \"pj1p95ebei8g3ro5p84kxxuuio\"}, \"name\": \"Mattermost\", \"type\": \"n8n-nodes-base.mattermost\", \"typeVersion\": 1, \"position\": [650, 300]}], \"connections\": {\"WooCommerce Trigger\": {\"main\": [[{\"node\": \"Mattermost\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"event\": \"fileCreated\", \"options\": {}, \"triggerOn\": \"specificFolder\", \"folderToWatch\": \"1HwOAKkkgveLji8vVpW9Xrg1EsBskwMNb\"}, \"name\": \"Google Drive Trigger\", \"type\": \"n8n-nodes-base.googleDriveTrigger\", \"typeVersion\": 1, \"position\": [250, 150]}, {\"parameters\": {\"text\": \"=A file in your Google Drive file folder has been created: {{$json[\\\"name\\\"]}}\", \"options\": {}, \"subject\": \"File Update\", \"toEmail\": \"<EMAIL>\", \"fromEmail\": \"<EMAIL>\"}, \"name\": \"Send Email\", \"type\": \"n8n-nodes-base.emailSend\", \"typeVersion\": 1, \"position\": [450, 150]}], \"connections\": {\"Google Drive Trigger\": {\"main\": [[{\"node\": \"Send Email\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Google Drive File Created Email Notification\", \"nodes\": [{\"parameters\": {\"folderId\": \"1HwOAKkkgveLji8vVpW9Xrg1EsBskwMNb\", \"event\": \"fileCreated\", \"triggerOn\": \"fileCreated\", \"pollTimes\": {\"item\": [{\"hour\": 0, \"minute\": 0}]}}, \"name\": \"Google Drive Trigger\", \"type\": \"n8n-nodes-base.googleDriveTrigger\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"to\": \"<EMAIL>\", \"from\": \"<EMAIL>\", \"subject\": \"File Update\", \"body\": \"A file in your Google Drive file folder has been created: {{$json[\\\"name\\\"]}}\"}, \"name\": \"Email\", \"type\": \"n8n-nodes-base.email\", \"typeVersion\": 1, \"position\": [650, 300]}], \"connections\": {\"Google Drive Trigger\": {\"main\": [[{\"node\": \"Email\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Multi AI Agents\", \"nodes\": [{\"parameters\": {\"aggregate\": \"aggregateAllItemData\", \"include\": \"specifiedFields\", \"fieldsToInclude\": \"title, raw_content\", \"options\": {}}, \"name\": \"Aggregate\", \"type\": \"n8n-nodes-base.aggregate\", \"typeVersion\": 1, \"position\": [2000, 280]}, {\"parameters\": {\"fieldToSplitOut\": \"results\", \"options\": {}}, \"name\": \"Split Out\", \"type\": \"n8n-nodes-base.splitOut\", \"typeVersion\": 1, \"position\": [1840, 280]}, {\"parameters\": {\"method\": \"POST\", \"url\": \"https://api.tavily.com/search\", \"sendBody\": true, \"specifyBody\": \"json\", \"jsonBody\": \"={\\n    \\\"api_key\\\": \\\"YOUR-API-KEY-HERE\\\",\\n    \\\"query\\\": \\\"{{ $json.query.replace(/\\\"/g, '\\\\\\\\\\\"') }}\\\",\\n    \\\"search_depth\\\": \\\"basic\\\",\\n    \\\"include_answer\\\": true,\\n    \\\"topic\\\": \\\"news\\\",\\n    \\\"include_raw_content\\\": true,\\n    \\\"max_results\\\": 3\\n} \", \"options\": {}}, \"name\": \"Search Internet\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [1680, 280]}, {\"parameters\": {\"options\": {}}, \"name\": \"OpenAI Chat Model1\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatOpenAi\", \"typeVersion\": 1, \"position\": [1540, 680]}, {\"parameters\": {\"options\": {}}, \"name\": \"OpenAI Chat Model2\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatOpenAi\", \"typeVersion\": 1, \"position\": [1860, 680]}, {\"parameters\": {\"operation\": \"update\", \"documentId\": {\"__rl\": true, \"value\": \"139g8ULrzBTi7GSSCAm7lXR8mDdc6IiKr8XXV_eN01lo\", \"mode\": \"list\", \"cachedResultName\": \"Content Creation\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/139g8ULrzBTi7GSSCAm7lXR8mDdc6IiKr8XXV_eN01lo/edit?usp=drivesdk\"}, \"sheetName\": {\"__rl\": true, \"value\": \"gid=0\", \"mode\": \"list\", \"cachedResultName\": \"Sheet1\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/139g8ULrzBTi7GSSCAm7lXR8mDdc6IiKr8XXV_eN01lo/edit#gid=0\"}, \"columns\": {\"mappingMode\": \"defineBelow\", \"value\": {\"Campaign\": \"={{ $('Google Sheets Trigger').item.json.Campaign }}\", \"Blog\": \"={{ $('Blog Writer').item.json.output }}\", \"LinkedIn\": \"={{ $('LinkedIn').item.json.output }}\", \"X\": \"={{ $('X').item.json.output }}\"}, \"matchingColumns\": [\"Campaign\"], \"schema\": [{\"id\": \"Campaign\", \"displayName\": \"Campaign\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"Content Subject\", \"displayName\": \"Content Subject\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": true}, {\"id\": \"Target Audience\", \"displayName\": \"Target Audience\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": true}, {\"id\": \"LinkedIn\", \"displayName\": \"LinkedIn\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"X\", \"displayName\": \"X\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true}, {\"id\": \"Blog\", \"displayName\": \"Blog\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true}, {\"id\": \"row_number\", \"displayName\": \"row_number\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"readOnly\": true, \"removed\": false}]}, \"options\": {}}, \"name\": \"Update Campaign\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.5, \"position\": [2180, 520]}, {\"parameters\": {\"options\": {}}, \"name\": \"OpenAI Chat Model\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatOpenAi\", \"typeVersion\": 1, \"position\": [1220, 680]}, {\"parameters\": {\"pollTimes\": {\"item\": [{\"mode\": \"everyMinute\"}]}, \"documentId\": {\"__rl\": true, \"value\": \"139g8ULrzBTi7GSSCAm7lXR8mDdc6IiKr8XXV_eN01lo\", \"mode\": \"list\", \"cachedResultName\": \"Content Creation\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/139g8ULrzBTi7GSSCAm7lXR8mDdc6IiKr8XXV_eN01lo/edit?usp=drivesdk\"}, \"sheetName\": {\"__rl\": true, \"value\": \"gid=0\", \"mode\": \"list\", \"cachedResultName\": \"Sheet1\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/139g8ULrzBTi7GSSCAm7lXR8mDdc6IiKr8XXV_eN01lo/edit#gid=0\"}, \"event\": \"rowAdded\", \"options\": {}}, \"name\": \"Google Sheets Trigger\", \"type\": \"n8n-nodes-base.googleSheetsTrigger\", \"typeVersion\": 1, \"position\": [1360, 280]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"b493a3c6-939a-4301-9257-055b80c28d7a\", \"name\": \"query\", \"value\": \"={{ $json['Content Subject'] }}\", \"type\": \"string\"}, {\"id\": \"e2813669-08fd-4d0d-a215-b0634032330b\", \"name\": \"targetAudience\", \"value\": \"={{ $json['Target Audience'] }}\", \"type\": \"string\"}]}, \"options\": {}}, \"name\": \"Set Search Fields\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [1520, 280]}, {\"parameters\": {\"agent\": \"conversationalAgent\", \"promptType\": \"define\", \"text\": \"=Article Content:\\n{{ $('Aggregate').item.json.data.toJsonString() }}\\n\\nTarget Audience:\\n{{ $('Set Search Fields').item.json.targetAudience }}\", \"options\": {\"systemMessage\": \"# System Role\\nYou are a skilled and creative blog writer, capable of crafting engaging, concise, and well-structured two-paragraph blog articles based on provided content.\\n\\n# Task Specification\\nWrite a two-paragraph blog article using the provided content. The blog should be coherent, engaging, and informative, tailored to a general audience. Ensure the tone is professional yet approachable, and the structure flows logically from introduction to conclusion.\\n\\n# Specifics and Context\\nThis task is essential for producing quick, high-quality blog articles that capture readers' attention while accurately conveying the intended message. By writing clear and engaging content, you help brands or individuals establish thought leadership and connect with their audience effectively.\\n\\n# Examples\\n## Example 1\\n**Input:**  \\nContent: \\\"Remote work has grown 44% in the last five years. Benefits include flexibility and reduced commute times. Challenges include maintaining productivity and combating isolation.\\\"\\n\\n**Output:**  \\nRemote work has become a transformative trend, with a 44% increase in adoption over the past five years. The appeal lies in its flexibility, allowing employees to tailor their schedules and eliminate time-consuming commutes. This shift has unlocked new possibilities for work-life balance and broadened the talent pool for businesses willing to embrace remote setups.\\n\\nHowever, remote work isn\\u2019t without its challenges. Employees often face difficulties in maintaining productivity outside a structured office environment and struggle with feelings of isolation. Addressing these concerns requires thoughtful solutions, such as virtual collaboration tools and strategies to foster connection, ensuring remote work remains both productive and fulfilling.\\n\\n## Example 2\\n**Input:**  \\nContent: \\\"The Mediterranean diet includes fruits, vegetables, whole grains, and healthy fats like olive oil. Studies show it reduces the risk of heart disease and supports brain health.\\\"\\n\\n**Output:**  \\nThe Mediterranean diet has long been celebrated as one of the healthiest eating patterns in the world. Emphasizing fresh fruits, vegetables, whole grains, and heart-healthy fats like olive oil, this diet is as delicious as it is nutritious. Its flavorful diversity makes it easy to adopt and sustain, whether you\\u2019re enjoying a vibrant Greek salad or a wholesome bowl of minestrone.\\n\\nWhat sets the Mediterranean diet apart is its scientifically backed health benefits. Numerous studies highlight its ability to reduce the risk of heart disease and support cognitive health, making it a cornerstone for longevity and wellness. By prioritizing natural, unprocessed foods, this lifestyle offers a sustainable approach to eating well and living better.\\n\\n# Reminders\\n- Maintain clarity and logical flow between paragraphs.\\n- Ensure the tone is engaging yet professional.\\n- Keep the blog concise and aligned with the provided content.\\n\"}}, \"name\": \"Blog Writer\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.7, \"position\": [1840, 520]}, {\"parameters\": {\"agent\": \"conversationalAgent\", \"promptType\": \"define\", \"text\": \"=Article Content:\\n{{ $json.data.toJsonString() }}\\n\\nTarget Audience:\\n{{ $('Set Search Fields').item.json.targetAudience }}\", \"options\": {\"systemMessage\": \"# System Role  \\nYou are an expert LinkedIn content creator specializing in transforming incoming articles into highly engaging posts tailored to a specific target audience.  \\n\\n# Task Specification  \\nUsing the provided article, craft a LinkedIn post that is:  \\n1. Written in a concise, engaging tone optimized for readability on mobile.  \\n2. Tailored specifically to the target audience\\u2019s interests, needs, and professional goals.  \\n3. Plain text only, with frequent line breaks for clarity.  \\n4. Incorporates 1-2 emojis to enhance personality and appeal.  \\n5. Provides actionable value and includes a clear call to action.  \\n6. Contains 3-5 relevant hashtags.  \\n7. Outputs only the post text\\u2014nothing else.  \\n\\n# Specifics and Context  \\nThe post should succinctly capture the core message of the article while resonating with the audience\\u2019s values. It must sound human and conversational, staying under 3,000 characters.  \\n\\n# Examples  \\n## Example 1  \\n**Input:** Article about productivity tips for managers.  \\n**Output:**  \\n\\ud83d\\udd25 Time to Supercharge Your Productivity!  \\n\\nManagers, are your days packed with back-to-back meetings and endless to-do lists? Here\\u2019s the secret to working smarter, not harder: [insight summary].  \\n\\n\\ud83d\\udc49 Top tips to stay ahead:  \\n1. Prioritize tasks using the Eisenhower Matrix.  \\n2. Block time on your calendar for deep focus.  \\n3. Delegate effectively to your team.  \\n\\nWhat strategies help you lead and manage your time effectively? Share your thoughts below!  \\n\\n#Leadership #TimeManagement #Productivity  \\n\\n# Reminders  \\n- Ensure the content aligns with the target audience's interests and challenges.  \\n- Always include 1-2 emojis and a call to action.  \\n- Use plain text and only output the post content.  \\n\"}}, \"name\": \"LinkedIn\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.7, \"position\": [1200, 520]}, {\"parameters\": {\"agent\": \"conversationalAgent\", \"promptType\": \"define\", \"text\": \"=Article Content:\\n{{ $('Aggregate').item.json.data.toJsonString() }}\\n\\nTarget Audience:\\n{{ $('Set Search Fields').item.json.targetAudience }}\", \"options\": {\"systemMessage\": \"=# System Role  \\nYou are an expert Twitter content creator specializing in transforming articles into engaging, concise tweets tailored to a specific target audience.  \\n\\n# Task Specification  \\nUsing the provided article, craft a tweet that is:  \\n1. Short, concise, and optimized for Twitter\\u2019s character limit (280 characters).  \\n2. Tailored to resonate with the target audience\\u2019s interests, needs, and goals.  \\n3. Incorporates 1-2 emojis to enhance personality and appeal.  \\n4. Offers value or insight and includes a clear call to action.  \\n5. Contains 1-3 relevant hashtags.  \\n6. Outputs only the tweet text\\u2014nothing else.  \\n\\n# Specifics and Context  \\nThe tweet should distill the essence of the article into a single impactful message. It must grab attention, provide immediate value, and encourage engagement (e.g., likes, replies, or clicks).  \\n\\n# Examples  \\n## Example 1  \\n**Input:** Article about productivity tips for managers.  \\n**Output:**  \\n\\ud83d\\udd25 Overwhelmed by meetings and to-dos? Managers, here\\u2019s how to stay ahead:  \\n- Use the Eisenhower Matrix.  \\n- Block focus time.  \\n- Delegate smarter.  \\n\\nWhat\\u2019s your top time-management hack? Let\\u2019s share! \\u23f0  \\n\\n#Leadership #Productivity  \\n\\n# Reminders  \\n- Keep the tone approachable and engaging.  \\n- Use emojis sparingly for emphasis.  \\n- Ensure the tweet stays within 280 characters and is tailored to the audience.  \\n- Only output the tweet text.  \\n\"}}, \"name\": \"X\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.7, \"position\": [1520, 520]}], \"connections\": {\"Split Out\": {\"main\": [[{\"node\": \"Aggregate\", \"type\": \"main\", \"index\": 0}]]}, \"Search Internet\": {\"main\": [[{\"node\": \"Split Out\", \"type\": \"main\", \"index\": 0}]]}, \"Aggregate\": {\"main\": [[{\"node\": \"LinkedIn\", \"type\": \"main\", \"index\": 0}]]}, \"OpenAI Chat Model1\": {\"ai_languageModel\": [[{\"node\": \"X\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"OpenAI Chat Model2\": {\"ai_languageModel\": [[{\"node\": \"Blog Writer\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"OpenAI Chat Model\": {\"ai_languageModel\": [[{\"node\": \"LinkedIn\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"Google Sheets Trigger\": {\"main\": [[{\"node\": \"Set Search Fields\", \"type\": \"main\", \"index\": 0}]]}, \"Set Search Fields\": {\"main\": [[{\"node\": \"Search Internet\", \"type\": \"main\", \"index\": 0}]]}, \"Blog Writer\": {\"main\": [[{\"node\": \"Update Campaign\", \"type\": \"main\", \"index\": 0}]]}, \"LinkedIn\": {\"main\": [[{\"node\": \"X\", \"type\": \"main\", \"index\": 0}]]}, \"X\": {\"main\": [[{\"node\": \"Blog Writer\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"GetOnboardingFileChainofThought\", \"nodes\": [{\"parameters\": {\"path\": \"ad3c9f90-e39e-49dc-b2f5-2a37fcda26ab\", \"responseMode\": \"responseNode\", \"options\": {}}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 2, \"position\": [0, 0]}, {\"parameters\": {\"operation\": \"get\", \"documentURL\": \"https://docs.google.com/document/d/1K0Y-OxyIV0auMN0CKe8tWUb4uSimFFIWZQCWaEvKQo4/view\"}, \"name\": \"Google Docs\", \"type\": \"n8n-nodes-base.googleDocs\", \"typeVersion\": 2, \"position\": [220, 0]}, {\"parameters\": {\"respondWith\": \"text\", \"responseBody\": \"={{ $json.content }}\", \"options\": {}}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1.1, \"position\": [440, 0]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Google Docs\", \"type\": \"main\", \"index\": 0}]]}, \"Google Docs\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"GetOnboardingFileChainofThought\", \"nodes\": [{\"parameters\": {\"httpMethod\": \"POST\", \"path\": \"getOnboardingFileChainofThought\", \"respondWith\": \"text\"}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"operation\": \"get\", \"documentId\": \"1K0Y-OxyIV0auMN0CKe8tWUb4uSimFFIWZQCWaEvKQo4\"}, \"name\": \"Google Docs\", \"type\": \"n8n-nodes-base.googleDocs\", \"typeVersion\": 1, \"position\": [220, 0]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Google Docs\", \"type\": \"main\", \"index\": 0}]]}, \"Google Docs\": {\"main\": [[{\"node\": \"Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"gmail triggered customer support\", \"nodes\": [{\"parameters\": {\"promptType\": \"define\", \"text\": \"=my email address is {{$json[\\\"From\\\"].match(/<(.*)>/)[1]}} and my query is ${{$json.snippet.split(/On |From:|Sent:|Subject:|-----Original Message-----/)[0].trim()}}\\n\", \"options\": {\"systemMessage\": \"=You are a helpful customer support assistant for the ecommerce store SugarMD, which sells medicines and health supplements. Today is {{ $now }}.\\n\\nYou will receive an incoming email.\\n\\nIf the email is a customer inquiry (for example, about an order, shipping, product, refund, or account), then respond helpfully and politely.\\n\\nIf the email is not a customer inquiry (for example, marketing emails, spam, newsletters, status updates), mark it as NOT a customer inquiry.\\n\\nYou must always output your response only as a JSON object in the following format:\\n\\n{\\n  \\\"customer_inquiry\\\": true or false,\\n  \\\"Message\\\": {\\n\\\"subject\\\":<subject>,\\n\\\"body\\\":<drafted HTML reply here>}\\n}\\n\\nThe customer_inquiry field must be either true or false (no quotes).\\n\\nThe Message field must contain a short, polite, HTML-formatted email response to the customer if it is an inquiry.It shouild also contain an appropriate subject\\n\\nIf customer_inquiry is false, set the Message to an empty string (\\\"\\\").\\n\\nAdditionally:\\n\\nYou can use the get_order_data tool if the customer is asking about their order. You must call it with the customer's email address.\\n\\nImportant:\\n\\nOnly output the JSON object.\\n\\nDo not output any explanation, notes, or extra text.\"}}, \"name\": \"AI Agent\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.9, \"position\": [-1260, -300]}, {\"parameters\": {\"model\": \"intelgpt4o\", \"options\": {}}, \"name\": \"Azure OpenAI Chat Model\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatAzureOpenAi\", \"typeVersion\": 1, \"position\": [-1460, -40]}, {\"parameters\": {\"pollTimes\": {\"item\": [{\"mode\": \"everyMinute\"}]}, \"filters\": {\"readStatus\": \"unread\"}}, \"name\": \"Gmail Trigger\", \"type\": \"n8n-nodes-base.gmailTrigger\", \"typeVersion\": 1.2, \"position\": [-1720, -300]}, {\"parameters\": {\"operation\": \"get\", \"messageId\": \"={{ $json.id }}\"}, \"name\": \"get message \", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [-1500, -300]}, {\"parameters\": {\"name\": \"get_order_data\", \"description\": \"call this tool to get shopify customer order data. the input must provide an email to identify the customer\", \"workflowId\": {\"__rl\": true, \"value\": \"LdzllH4oj6UM6Ie6\", \"mode\": \"list\", \"cachedResultName\": \"get costomer order data\"}, \"workflowInputs\": {\"mappingMode\": \"defineBelow\", \"value\": {\"email\": \"={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('email', ``, 'string') }}\"}, \"matchingColumns\": [], \"schema\": [{\"id\": \"email\", \"displayName\": \"email\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"canBeUsedToMatch\": true, \"type\": \"string\", \"removed\": false}, {\"id\": \"order_name\", \"displayName\": \"order_name\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"canBeUsedToMatch\": true, \"type\": \"string\", \"removed\": true}], \"attemptToConvertTypes\": false, \"convertFieldsToString\": false}}, \"name\": \"get user's order data\", \"type\": \"@n8n/n8n-nodes-langchain.toolWorkflow\", \"typeVersion\": 2.1, \"position\": [-1160, -40]}, {\"parameters\": {\"sendTo\": \"={{ $('get message ').item.json.From.match(/<(.*)>/)[1] }}\", \"subject\": \"={{JSON.parse($json.output).Message.subject}}\", \"message\": \"={{JSON.parse($json.output).Message.body}}\", \"options\": {}}, \"name\": \"Gmail\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [-540, -320]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"loose\", \"version\": 2}, \"conditions\": [{\"id\": \"7d366820-aece-4b08-86ea-045e9d3f6331\", \"leftValue\": \"={{ JSON.parse($json.output).customer_inquiry}}\\n\", \"rightValue\": \"true\", \"operator\": {\"type\": \"boolean\", \"operation\": \"true\", \"singleValue\": true}}], \"combinator\": \"and\"}, \"looseTypeValidation\": true, \"options\": {}}, \"name\": \"If\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2.2, \"position\": [-880, -300]}, {\"parameters\": {\"sessionIdType\": \"customKey\", \"sessionKey\": \"={{$json[\\\"From\\\"].match(/<(.*)>/)[1]}}\"}, \"name\": \"Simple Memory\", \"type\": \"@n8n/n8n-nodes-langchain.memoryBufferWindow\", \"typeVersion\": 1.3, \"position\": [-1320, -40]}, {\"parameters\": {\"tableName\": {\"__rl\": true, \"value\": \"documents\", \"mode\": \"list\", \"cachedResultName\": \"documents\"}, \"options\": {\"queryName\": \"match_documents\"}}, \"name\": \"Supabase Vector Store\", \"type\": \"@n8n/n8n-nodes-langchain.vectorStoreSupabase\", \"typeVersion\": 1.1, \"position\": [-1120, 180]}, {\"parameters\": {\"model\": \"inteltextembeddingada002\", \"options\": {}}, \"name\": \"Embeddings Azure OpenAI\", \"type\": \"@n8n/n8n-nodes-langchain.embeddingsAzureOpenAi\", \"typeVersion\": 1, \"position\": [-1080, 340]}, {\"parameters\": {\"model\": \"intelgpt4o\", \"options\": {}}, \"name\": \"Azure OpenAI Chat Model1\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatAzureOpenAi\", \"typeVersion\": 1, \"position\": [-820, 180]}, {\"parameters\": {\"name\": \"user_documents\", \"description\": \"contains the store's policies on shipping and returns\"}, \"name\": \"retrieve documents\", \"type\": \"@n8n/n8n-nodes-langchain.toolVectorStore\", \"typeVersion\": 1, \"position\": [-1000, -40]}], \"connections\": {\"Azure OpenAI Chat Model\": {\"ai_languageModel\": [[{\"node\": \"AI Agent\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"AI Agent\": {\"main\": [[{\"node\": \"If\", \"type\": \"main\", \"index\": 0}]]}, \"Gmail Trigger\": {\"main\": [[{\"node\": \"get message \", \"type\": \"main\", \"index\": 0}]]}, \"get message \": {\"main\": [[{\"node\": \"AI Agent\", \"type\": \"main\", \"index\": 0}]]}, \"get user's order data\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"If\": {\"main\": [[{\"node\": \"Gmail\", \"type\": \"main\", \"index\": 0}]]}, \"Simple Memory\": {\"ai_memory\": [[{\"node\": \"AI Agent\", \"type\": \"ai_memory\", \"index\": 0}]]}, \"Supabase Vector Store\": {\"ai_vectorStore\": [[{\"node\": \"retrieve documents\", \"type\": \"ai_vectorStore\", \"index\": 0}]]}, \"Embeddings Azure OpenAI\": {\"ai_embedding\": [[{\"node\": \"Supabase Vector Store\", \"type\": \"ai_embedding\", \"index\": 0}]]}, \"Azure OpenAI Chat Model1\": {\"ai_languageModel\": [[{\"node\": \"retrieve documents\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"retrieve documents\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {}, \"name\": \"Read PDF\", \"type\": \"n8n-nodes-base.readPDF\", \"typeVersion\": 1, \"position\": [860, 1420]}, {\"parameters\": {\"width\": 444.034812880766, \"height\": 599.5274151436035, \"content\": \"## Send specific PDF attachments from Gmail to Google Drive using OpenAI\\n\\n_**DISCLAIMER**: You may have varying success when using this workflow so be prepared to validate the correctness of OpenAI's results._\\n\\nThis workflow reads PDF textual content and sends the text to OpenAI. Attachments of interest will then be uploaded to a specified Google Drive folder. For example, you may wish to send invoices received from an email to an inbox folder in Google Drive for later processing. This workflow has been designed to easily change the search term to match your needs. See the workflow for more details.\\n\\n### How it works\\n1. Triggers off on the `On email received` node.\\n2. Iterates over the attachments in the email.\\n3. Uses the `OpenAI` node to filter out the attachments that do not match the search term set in the `Configure` node. You could match on various PDF files (i.e. invoice, receipt, or contract).\\n4. If the PDF attachment matches the search term, the workflow uses the `Google Drive` node to upload the PDF attachment to a specific Google Drive folder.\\n\\n\\nWorkflow written by [David Sha](https://davidsha.me).\"}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-760, 1300]}, {\"parameters\": {\"values\": {\"number\": [{\"name\": \"maxTokenSize\", \"value\": 4000}, {\"name\": \"replyTokenSize\", \"value\": 50}], \"string\": [{\"name\": \"Match on\", \"value\": \"payslip\"}, {\"name\": \"Google Drive folder to upload matched PDFs\", \"value\": \"https://drive.google.com/drive/u/0/folders/1SKdHTnYoBNlnhF_QJ-Zyepy-3-WZkObo\"}]}, \"options\": {}}, \"name\": \"Configure\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [-20, 1520]}, {\"parameters\": {\"conditions\": {\"string\": [{\"value1\": \"={{ $binary.data.fileExtension }}\", \"value2\": \"pdf\"}]}}, \"name\": \"Is PDF\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 1, \"position\": [640, 1520]}, {\"parameters\": {}, \"name\": \"Not a PDF\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [860, 1620]}, {\"parameters\": {\"conditions\": {\"string\": [{\"value1\": \"={{ $json[\\\"text\\\"] }}\", \"value2\": \"true\"}]}}, \"name\": \"Is matched\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 1, \"position\": [1720, 1480]}, {\"parameters\": {}, \"name\": \"This is a matched PDF\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [1940, 1380]}, {\"parameters\": {}, \"name\": \"This is not a matched PDF\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [1940, 1580]}, {\"parameters\": {\"jsCode\": \"// https://community.n8n.io/t/iterating-over-email-attachments/13588/3\\nlet results = [];\\n\\nfor (const item of $input.all()) {\\n  for (key of Object.keys(item.binary)) {\\n        results.push({\\n            json: {},\\n            binary: {\\n                data: item.binary[key],\\n            }\\n        });\\n    }\\n}\\n\\nreturn results;\"}, \"name\": \"Iterate over email attachments\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 1, \"position\": [420, 1420]}, {\"parameters\": {\"prompt\": \"=Does this PDF file look like a {{ $(\\\"Configure\\\").first().json[\\\"Match on\\\"] }}? Return \\\"true\\\" if it is a {{ $(\\\"Configure\\\").first().json[\\\"Match on\\\"] }} and \\\"false\\\" if not. Only reply with lowercase letters \\\"true\\\" or \\\"false\\\".\\n\\nThis is the PDF filename:\\n```\\n{{ $binary.data.fileName }}\\n```\\n\\nThis is the PDF text content:\\n```\\n{{ $json.text }}\\n```\", \"options\": {\"maxTokens\": \"={{ $('Configure').first().json.replyTokenSize }}\", \"temperature\": 0.1}}, \"name\": \"OpenAI matches PDF textual content\", \"type\": \"n8n-nodes-base.openAi\", \"typeVersion\": 1, \"position\": [1300, 1340]}, {\"parameters\": {\"mode\": \"combine\", \"options\": {\"clashHandling\": {\"values\": {\"resolveClash\": \"preferInput1\"}}}, \"combinationMode\": \"mergeByPosition\"}, \"name\": \"Merge\", \"type\": \"n8n-nodes-base.merge\", \"typeVersion\": 2, \"position\": [1500, 1480]}, {\"parameters\": {\"name\": \"={{ $binary.data.fileName }}\", \"options\": {}, \"parents\": [\"={{ $('Configure').first().json[\\\"Google Drive folder to upload matched PDFs\\\"].split(\\\"/\\\").at(-1) }}\"], \"binaryData\": true}, \"name\": \"Upload file to folder\", \"type\": \"n8n-nodes-base.googleDrive\", \"typeVersion\": 2, \"position\": [2160, 1380]}, {\"parameters\": {\"simple\": false, \"filters\": {}, \"options\": {\"downloadAttachments\": true, \"dataPropertyAttachmentsPrefixName\": \"attachment_\"}, \"pollTimes\": {\"item\": [{\"mode\": \"everyMinute\"}]}}, \"name\": \"On email received\", \"type\": \"n8n-nodes-base.gmailTrigger\", \"typeVersion\": 1, \"position\": [-240, 1520]}, {\"parameters\": {\"width\": 259.0890718059702, \"height\": 607.9684549079709, \"content\": \"### Configuration\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n__`Match on`(required)__: What should OpenAI's search term be? Examples: invoice, callsheet, receipt, contract, payslip.\\n__`Google Drive folder to upload matched PDFs`(required)__: Paste the link of the GDrive folder, an example has been provided but will need to change to a folder you own.\\n__`maxTokenSize`(required)__: The maximum token size for the model you choose. See possible models from OpenAI [here](https://platform.openai.com/docs/models/gpt-3).\\n__`replyTokenSize`(required)__: The reply's maximum token size. Default is 300. This determines how much text the AI will reply with.\"}, \"name\": \"Note5\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-100, 1440]}, {\"parameters\": {}, \"name\": \"Ignore large PDFs\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [1300, 1620]}, {\"parameters\": {\"conditions\": {\"boolean\": [{\"value1\": \"={{ $json.text.length() / 4 <= $('Configure').first().json.maxTokenSize - $('Configure').first().json.replyTokenSize }}\", \"value2\": true}]}}, \"name\": \"Is text within token limit?\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 1, \"position\": [1080, 1520]}, {\"parameters\": {\"conditions\": {\"boolean\": [{\"value1\": \"={{ $('On email received').item.binary.isNotEmpty() }}\", \"value2\": true}]}}, \"name\": \"Has attachments?\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 1, \"position\": [200, 1520]}, {\"parameters\": {}, \"name\": \"There are no attachments\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [420, 1620]}], \"connections\": {\"Merge\": {\"main\": [[{\"node\": \"Is matched\", \"type\": \"main\", \"index\": 0}]]}, \"Is PDF\": {\"main\": [[{\"node\": \"Read PDF\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Not a PDF\", \"type\": \"main\", \"index\": 0}]]}, \"Read PDF\": {\"main\": [[{\"node\": \"Is text within token limit?\", \"type\": \"main\", \"index\": 0}]]}, \"Configure\": {\"main\": [[{\"node\": \"Has attachments?\", \"type\": \"main\", \"index\": 0}]]}, \"Is matched\": {\"main\": [[{\"node\": \"This is a matched PDF\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"This is not a matched PDF\", \"type\": \"main\", \"index\": 0}]]}, \"Has attachments?\": {\"main\": [[{\"node\": \"Iterate over email attachments\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"There are no attachments\", \"type\": \"main\", \"index\": 0}]]}, \"On email received\": {\"main\": [[{\"node\": \"Configure\", \"type\": \"main\", \"index\": 0}]]}, \"This is a matched PDF\": {\"main\": [[{\"node\": \"Upload file to folder\", \"type\": \"main\", \"index\": 0}]]}, \"Is text within token limit?\": {\"main\": [[{\"node\": \"OpenAI matches PDF textual content\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Merge\", \"type\": \"main\", \"index\": 1}], [{\"node\": \"Ignore large PDFs\", \"type\": \"main\", \"index\": 0}]]}, \"Iterate over email attachments\": {\"main\": [[{\"node\": \"Is PDF\", \"type\": \"main\", \"index\": 0}]]}, \"OpenAI matches PDF textual content\": {\"main\": [[{\"node\": \"Merge\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {}, \"name\": \"On clicking 'execute'\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [250, 300]}, {\"parameters\": {\"text\": \"This is a test workflow for the twitter node\", \"additionalFields\": {}}, \"name\": \"Twitter\", \"type\": \"n8n-nodes-base.twitter\", \"typeVersion\": 1, \"position\": [450, 300]}], \"connections\": {\"On clicking 'execute'\": {\"main\": [[{\"node\": \"Twitter\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Twitter\", \"nodes\": [{\"parameters\": {}, \"name\": \"manualTrigger\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [250, 300]}, {\"parameters\": {\"text\": \"This is a test workflow for the twitter node\", \"credentials\": {\"twitter\": \"twitter-credentials\"}}, \"name\": \"Twitter\", \"type\": \"n8n-nodes-base.twitter\", \"typeVersion\": 1, \"position\": [450, 300]}], \"connections\": {\"manualTrigger\": {\"main\": [[{\"node\": \"Twitter\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {}, \"name\": \"On clicking 'execute'\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [240, 300]}, {\"parameters\": {\"url\": \"https://www.ardaudiothek.de/sendung/kalk-und-welk/10777871/\", \"options\": {}, \"responseFormat\": \"string\"}, \"name\": \"Get overview page\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 2, \"position\": [460, 300]}, {\"parameters\": {\"options\": {}, \"extractionValues\": {\"values\": [{\"key\": \"links\", \"attribute\": \"href\", \"cssSelector\": \"a[href*=\\\"/episode/\\\"]\", \"returnArray\": true, \"returnValue\": \"attribute\"}]}}, \"name\": \"Extract links\", \"type\": \"n8n-nodes-base.htmlExtract\", \"typeVersion\": 1, \"position\": [680, 300]}, {\"parameters\": {\"operation\": \"removeDuplicates\"}, \"name\": \"Remove duplicate links\", \"type\": \"n8n-nodes-base.itemLists\", \"typeVersion\": 1, \"position\": [1120, 300]}, {\"parameters\": {\"options\": {\"destinationFieldName\": \"link\"}, \"fieldToSplitOut\": \"links\"}, \"name\": \"Split out lists\", \"type\": \"n8n-nodes-base.itemLists\", \"typeVersion\": 1, \"position\": [900, 300]}, {\"parameters\": {\"url\": \"=https://www.ardaudiothek.de{{ $json[\\\"link\\\"] }}\", \"options\": {}, \"responseFormat\": \"string\"}, \"name\": \"Get episode page\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 2, \"position\": [1340, 300]}, {\"parameters\": {\"options\": {}, \"extractionValues\": {\"values\": [{\"key\": \"script\", \"cssSelector\": \"script:nth-of-type(2)\", \"returnValue\": \"html\"}]}}, \"name\": \"Extract script\", \"type\": \"n8n-nodes-base.htmlExtract\", \"typeVersion\": 1, \"position\": [1560, 300]}, {\"parameters\": {\"values\": {\"string\": [{\"name\": \"data\", \"value\": \"={{ JSON.parse($json.script) }}\"}]}, \"options\": {}, \"keepOnlySet\": true}, \"name\": \"Parse JSON\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [1780, 300]}, {\"parameters\": {\"functionCode\": \"const escapeHTML = str => str.replace(/[&<>'\\\"]/g, \\n  tag => ({\\n      '&': '&amp;',\\n      '<': '&lt;',\\n      '>': '&gt;',\\n      \\\"'\\\": '&#39;',\\n      '\\\"': '&quot;'\\n    }[tag]));\\n\\nlet feedItems = [];\\nfor (item of items) {\\n  feedItems.push(`<item>\\n  <title>${escapeHTML(item.json.data.name)}</title>\\n  <enclosure url=\\\"${item.json.data.associatedMedia.contentUrl}\\\" length=\\\"${item.json.data.timeRequired * 20 * 1000}\\\" type=\\\"${item.json.data.encodingFormat}\\\"/>\\n  <guid isPermaLink=\\\"false\\\">${item.json.data.identifier}</guid>\\n  <pubDate>${DateTime.fromISO(item.json.data.datePublished).toRFC2822()}</pubDate>\\n  <description>${escapeHTML(item.json.data.description)}</description>\\n</item>`);\\n}\\n\\nreturn [{\\n  data: `<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\\n<rss version=\\\"2.0\\\" xmlns:itunes=\\\"http://www.itunes.com/dtds/podcast-1.0.dtd\\\" xmlns:content=\\\"http://purl.org/rss/1.0/modules/content/\\\">\\n  <channel>\\n    <title>${escapeHTML(items[0].json.data.partOfSeries.name)}</title>\\n    <description>${escapeHTML(items[0].json.data.partOfSeries.about)}</description>\\n    <itunes:image href=\\\"${escapeHTML(items[0].json.data.image)}\\\" />\\n    <language>${items[0].json.data.inLanguage}</language>\\n    <itunes:category text=\\\"Comedy\\\" />\\n    <itunes:explicit>no</itunes:explicit>\\n    <link>${items[0].json.data.partOfSeries.url}</link>\\n    <copyright>\\u00a9 ${$now.toFormat('yyyy')} ${escapeHTML(items[0].json.data.productionCompany)}</copyright>\\n    <itunes:author>${escapeHTML(items[0].json.data.productionCompany)}</itunes:author>\\n    ${feedItems.join('\\\\n')}\\n  </channel>\\n</rss>\\n`\\n}];\\n\"}, \"name\": \"Define feed items\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [2000, 300]}, {\"parameters\": {\"path\": \"3fbd94de-2fb3-4b32-a46e-c237865479b9.rss\", \"options\": {}, \"responseMode\": \"responseNode\"}, \"name\": \"Feed\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [240, 100]}, {\"parameters\": {\"options\": {\"responseCode\": 200, \"responseHeaders\": {\"entries\": [{\"name\": \"Content-Type\", \"value\": \"application/rss+xml\"}]}}, \"respondWith\": \"text\", \"responseBody\": \"={{ $json[\\\"data\\\"] }}\"}, \"name\": \"Serve feed\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1, \"position\": [2220, 300]}], \"connections\": {\"Feed\": {\"main\": [[{\"node\": \"Get overview page\", \"type\": \"main\", \"index\": 0}]]}, \"Parse JSON\": {\"main\": [[{\"node\": \"Define feed items\", \"type\": \"main\", \"index\": 0}]]}, \"Extract links\": {\"main\": [[{\"node\": \"Split out lists\", \"type\": \"main\", \"index\": 0}]]}, \"Extract script\": {\"main\": [[{\"node\": \"Parse JSON\", \"type\": \"main\", \"index\": 0}]]}, \"Split out lists\": {\"main\": [[{\"node\": \"Remove duplicate links\", \"type\": \"main\", \"index\": 0}]]}, \"Get episode page\": {\"main\": [[{\"node\": \"Extract script\", \"type\": \"main\", \"index\": 0}]]}, \"Define feed items\": {\"main\": [[{\"node\": \"Serve feed\", \"type\": \"main\", \"index\": 0}]]}, \"Get overview page\": {\"main\": [[{\"node\": \"Extract links\", \"type\": \"main\", \"index\": 0}]]}, \"On clicking 'execute'\": {\"main\": [[{\"node\": \"Get overview page\", \"type\": \"main\", \"index\": 0}]]}, \"Remove duplicate links\": {\"main\": [[{\"node\": \"Get episode page\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"<PERSON><PERSON> & Welk RSS Feed\", \"nodes\": [{\"parameters\": {\"path\": \"kalkwelk.rss\", \"response\": \"responseNode\"}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"url\": \"https://www.ardaudiothek.de/podcast/kalk-welk\", \"continueOnFail\": true}, \"name\": \"HTTP Request\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 1, \"position\": [200, 0]}, {\"parameters\": {\"cssSelector\": \"a\", \"data\": \"href\"}, \"name\": \"Extract Links\", \"type\": \"n8n-nodes-base.html\", \"typeVersion\": 1, \"position\": [400, 0]}, {\"parameters\": {\"batchSize\": 1}, \"name\": \"Split In Batches\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 1, \"position\": [600, 0]}, {\"parameters\": {\"url\": \"={{$node[\\\"Split In Batches\\\"].data[\\\"url\\\"]}}\", \"continueOnFail\": true}, \"name\": \"HTTP Request (Episode Page)\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 1, \"position\": [800, 0]}, {\"parameters\": {\"cssSelector\": \"script#playerConfig\", \"data\": \"innerHTML\"}, \"name\": \"Extract JSON Data\", \"type\": \"n8n-nodes-base.html\", \"typeVersion\": 1, \"position\": [1000, 0]}, {\"parameters\": {\"json\": \"={{$node[\\\"Extract JSON Data\\\"].data[\\\"data\\\"]}}\"}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [1200, 0]}, {\"parameters\": {\"mode\": \"combine\", \"combineBy\": \"combineAll\"}, \"name\": \"Merge\", \"type\": \"n8n-nodes-base.merge\", \"typeVersion\": 1, \"position\": [1400, 0]}, {\"parameters\": {\"xml\": \"<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\\n<rss version=\\\"2.0\\\">\\n  <channel>\\n    <title>Kalk & Welk</title>\\n    <link>https://www.ardaudiothek.de/podcast/kalk-welk</link>\\n    <description>Kalk & Welk</description>\\n    <language>de</language>\\n    <lastBuildDate>{{ $now }}</lastBuildDate>\\n    <generator>n8n</generator>\\n    {{#each items}}\\n    <item>\\n      <title>{{ $this.title }}</title>\\n      <link>{{ $this.link }}</link>\\n      <description>{{ $this.description }}</description>\\n      <pubDate>{{ $this.pubDate }}</pubDate>\\n      <enclosure url=\\\"{{ $this.enclosure.url }}\\\" type=\\\"{{ $this.enclosure.type }}\\\" />\\n    </item>\\n    {{/each}}\\n  </channel>\\n</rss>\"}, \"name\": \"Set (RSS Feed)\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [1600, 0]}, {\"parameters\": {\"responseCode\": 200, \"responseBody\": \"={{$node[\\\"Set (RSS Feed)\\\"]}}\"}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1, \"position\": [1800, 0]}, {\"parameters\": {\"error\": \"Error fetching data\"}, \"name\": \"Set (Error)\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [1600, 200]}, {\"parameters\": {\"responseCode\": 500, \"responseBody\": \"={{$node[\\\"Set (Error)\\\"]}}\"}, \"name\": \"Respond to Webhook (Error)\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1, \"position\": [1800, 200]}, {\"parameters\": {\"onError\": \"continueError\"}, \"name\": \"Try/Catch\", \"type\": \"n8n-nodes-base.tryCatch\", \"typeVersion\": 1, \"position\": [1000, 200]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"HTTP Request\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request\": {\"main\": [[{\"node\": \"Extract Links\", \"type\": \"main\", \"index\": 0}]]}, \"Extract Links\": {\"main\": [[{\"node\": \"Split In Batches\", \"type\": \"main\", \"index\": 0}]]}, \"Split In Batches\": {\"main\": [[{\"node\": \"HTTP Request (Episode Page)\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request (Episode Page)\": {\"main\": [[{\"node\": \"Extract JSON Data\", \"type\": \"main\", \"index\": 0}]]}, \"Extract JSON Data\": {\"main\": [[{\"node\": \"Set\", \"type\": \"main\", \"index\": 0}]]}, \"Set\": {\"main\": [[{\"node\": \"Merge\", \"type\": \"main\", \"index\": 0}]]}, \"Merge\": {\"main\": [[{\"node\": \"Set (RSS Feed)\", \"type\": \"main\", \"index\": 0}]]}, \"Set (RSS Feed)\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}, \"Set (Error)\": {\"main\": [[{\"node\": \"Respond to Webhook (Error)\", \"type\": \"main\", \"index\": 0}]]}, \"Try/Catch\": {\"main\": [[{\"node\": \"Set\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Set (Error)\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Knowledge Base Update: GeneReviews\", \"nodes\": [{\"parameters\": {}, \"name\": \"When clicking \\u2018Test workflow\\u2019\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [-1900, 1020]}, {\"parameters\": {\"url\": \"https://ftp.ncbi.nlm.nih.gov/pub/litarch/ca/84/gene_NBK1116.tar.gz\", \"options\": {\"allowUnauthorizedCerts\": false, \"response\": {\"response\": {\"responseFormat\": \"file\"}}}}, \"name\": \"HTTP Request\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [-640, 220]}, {\"parameters\": {}, \"name\": \"Execute Command\", \"type\": \"n8n-nodes-base.executeCommand\", \"typeVersion\": 1, \"position\": [-220, 220]}, {\"parameters\": {\"operation\": \"write\", \"fileName\": \"=/tmp/gene_NBK1116.tar.gz\", \"options\": {}}, \"name\": \"Read/Write Files from Disk\", \"type\": \"n8n-nodes-base.readWriteFile\", \"typeVersion\": 1, \"position\": [-440, 220]}, {\"parameters\": {\"fileSelector\": \"/tmp/genereviews_extracted_files/gene_NBK1116/*.pdf\", \"options\": {}}, \"name\": \"Read/Write Files from Disk1\", \"type\": \"n8n-nodes-base.readWriteFile\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"mode\": \"insert\", \"pineconeIndex\": {\"__rl\": true, \"value\": \"guideline\", \"mode\": \"list\", \"cachedResultName\": \"guideline\"}, \"options\": {}}, \"name\": \"Pinecone Vector Store\", \"type\": \"@n8n/n8n-nodes-langchain.vectorStorePinecone\", \"typeVersion\": 1, \"position\": [600, 160]}, {\"parameters\": {\"options\": {\"metadata\": {\"metadataValues\": [{\"name\": \"documentTitle\", \"value\": \"={{ $('Extract from File').item.json.info.Title }}\"}, {\"name\": \"documentVersion\", \"value\": \"={{ $('Extract from File').item.json.version }}\"}, {\"name\": \"DocumentCreator\", \"value\": \"={{ $('Extract from File').item.json.info.Creator }}\"}]}}}, \"name\": \"Default Data Loader\", \"type\": \"@n8n/n8n-nodes-langchain.documentDefaultDataLoader\", \"typeVersion\": 1, \"position\": [720, 420]}, {\"parameters\": {\"chunkSize\": 3000, \"chunkOverlap\": 200, \"options\": {}}, \"name\": \"Recursive Character Text Splitter\", \"type\": \"@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter\", \"typeVersion\": 1, \"position\": [660, 620]}, {\"parameters\": {\"model\": \"text-embedding-3-large\", \"options\": {}}, \"name\": \"Embeddings OpenAI\", \"type\": \"@n8n/n8n-nodes-langchain.embeddingsOpenAi\", \"typeVersion\": 1.2, \"position\": [480, 420]}, {\"parameters\": {\"options\": {}}, \"name\": \"Loop Over Items\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [240, 20]}, {\"parameters\": {\"operation\": \"pdf\", \"options\": {}}, \"name\": \"Extract from File\", \"type\": \"n8n-nodes-base.extractFromFile\", \"typeVersion\": 1, \"position\": [420, 100]}, {\"parameters\": {\"options\": {\"metadata\": {\"metadataValues\": [{\"name\": \"documentTitle\", \"value\": \"={{ $('Merge').item.json.info.Title}}\"}, {\"name\": \"documentVersion\", \"value\": \"={{ $('Merge').item.json.version }}\"}, {\"name\": \"DocumentCreator\", \"value\": \"={{ $('Merge').item.json.info.Creator }}\"}]}}}, \"name\": \"Default Data Loader1\", \"type\": \"@n8n/n8n-nodes-langchain.documentDefaultDataLoader\", \"typeVersion\": 1, \"position\": [1020, 1440]}, {\"parameters\": {\"chunkSize\": 3000, \"chunkOverlap\": 200, \"options\": {}}, \"name\": \"Recursive Character Text Splitter1\", \"type\": \"@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter\", \"typeVersion\": 1, \"position\": [1180, 1680]}, {\"parameters\": {\"model\": \"text-embedding-3-large\", \"options\": {}}, \"name\": \"Embeddings OpenAI1\", \"type\": \"@n8n/n8n-nodes-langchain.embeddingsOpenAi\", \"typeVersion\": 1.2, \"position\": [680, 1480]}, {\"parameters\": {\"mode\": \"chooseBranch\"}, \"name\": \"Merge\", \"type\": \"n8n-nodes-base.merge\", \"typeVersion\": 3, \"position\": [520, 1040]}, {\"parameters\": {}, \"name\": \"Execute Command1\", \"type\": \"n8n-nodes-base.executeCommand\", \"typeVersion\": 1, \"position\": [-1260, 880]}, {\"parameters\": {\"mode\": \"chooseBranch\", \"useDataOfInput\": 2}, \"name\": \"Merge1\", \"type\": \"n8n-nodes-base.merge\", \"typeVersion\": 3, \"position\": [1200, 1140]}, {\"parameters\": {\"url\": \"https://ftp.ncbi.nlm.nih.gov/pub/litarch/ca/84/gene_NBK1116.tar.gz\", \"options\": {\"allowUnauthorizedCerts\": false, \"response\": {\"response\": {\"responseFormat\": \"file\"}}}}, \"name\": \"HTTP Request: Download genereviews tar.gz file\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [-1680, 880]}, {\"parameters\": {\"operation\": \"write\", \"fileName\": \"=/tmp/gene_NBK1116.tar.gz\", \"options\": {}}, \"name\": \"Write genereviews tar.gz file to disk\", \"type\": \"n8n-nodes-base.readWriteFile\", \"typeVersion\": 1, \"position\": [-1460, 880]}, {\"parameters\": {\"fileSelector\": \"/tmp/genereviews_extracted_files/gene_NBK1116/hht.pdf\", \"options\": {}}, \"name\": \"Read genereviews files from disk\", \"type\": \"n8n-nodes-base.readWriteFile\", \"typeVersion\": 1, \"position\": [-1060, 880]}, {\"parameters\": {\"options\": {}}, \"name\": \"Loop Over genereviews files\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [-660, 1100]}, {\"parameters\": {\"operation\": \"pdf\", \"options\": {}}, \"name\": \"PDF to JSON\", \"type\": \"n8n-nodes-base.extractFromFile\", \"typeVersion\": 1, \"position\": [-480, 920]}, {\"parameters\": {\"assignments\": {\"assignments\": []}, \"includeOtherFields\": true, \"options\": {}}, \"name\": \"Capture Original File JSON\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [-240, 1100]}, {\"parameters\": {\"operation\": \"get\", \"tableId\": \"geneReviewsIndex\", \"filters\": {\"conditions\": [{\"keyName\": \"title\", \"keyValue\": \"={{$json.info.Title}}\"}, {\"keyName\": \"version\", \"keyValue\": \"={{ $json.version }}\"}, {\"keyName\": \"creator\", \"keyValue\": \"={{ $json.info.Creator }}\"}]}}, \"name\": \"Read Document from Supabase Index\", \"type\": \"n8n-nodes-base.supabase\", \"typeVersion\": 1, \"position\": [0, 940]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\", \"version\": 2}, \"conditions\": [{\"id\": \"4955ca4f-d8f9-49ec-920b-6e93c1b65109\", \"leftValue\": \"={{ $json.id }}\", \"rightValue\": 0, \"operator\": {\"type\": \"number\", \"operation\": \"exists\", \"singleValue\": true}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"Check if document is on Supabase Index\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2.2, \"position\": [240, 940]}, {\"parameters\": {\"mode\": \"insert\", \"pineconeIndex\": {\"__rl\": true, \"value\": \"guideline\", \"mode\": \"id\"}, \"options\": {}}, \"name\": \"Write Document to Pinecone (guideline)\", \"type\": \"@n8n/n8n-nodes-langchain.vectorStorePinecone\", \"typeVersion\": 1, \"position\": [780, 980]}, {\"parameters\": {\"tableId\": \"geneReviewsIndex\", \"fieldsUi\": {\"fieldValues\": [{\"fieldId\": \"title\", \"fieldValue\": \"={{ $('PDF to JSON').item.json.info.Title}}\"}, {\"fieldId\": \"creator\", \"fieldValue\": \"={{ $('PDF to JSON').item.json.info.Creator }}\"}, {\"fieldId\": \"version\", \"fieldValue\": \"={{ $('PDF to JSON').item.json.version }}\"}]}}, \"name\": \"Write document metadata to Supabase\", \"type\": \"n8n-nodes-base.supabase\", \"typeVersion\": 1, \"position\": [1460, 1160]}], \"connections\": {\"When clicking \\u2018Test workflow\\u2019\": {\"main\": [[{\"node\": \"HTTP Request: Download genereviews tar.gz file\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request\": {\"main\": [[{\"node\": \"Read/Write Files from Disk\", \"type\": \"main\", \"index\": 0}]]}, \"Read/Write Files from Disk1\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Default Data Loader\": {\"ai_document\": [[{\"node\": \"Pinecone Vector Store\", \"type\": \"ai_document\", \"index\": 0}]]}, \"Recursive Character Text Splitter\": {\"ai_textSplitter\": [[{\"node\": \"Default Data Loader\", \"type\": \"ai_textSplitter\", \"index\": 0}]]}, \"Embeddings OpenAI\": {\"ai_embedding\": [[{\"node\": \"Pinecone Vector Store\", \"type\": \"ai_embedding\", \"index\": 0}]]}, \"Loop Over Items\": {\"main\": [[], [{\"node\": \"Extract from File\", \"type\": \"main\", \"index\": 0}]]}, \"Pinecone Vector Store\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Extract from File\": {\"main\": [[{\"node\": \"Pinecone Vector Store\", \"type\": \"main\", \"index\": 0}]]}, \"Default Data Loader1\": {\"ai_document\": [[{\"node\": \"Write Document to Pinecone (guideline)\", \"type\": \"ai_document\", \"index\": 0}]]}, \"Recursive Character Text Splitter1\": {\"ai_textSplitter\": [[{\"node\": \"Default Data Loader1\", \"type\": \"ai_textSplitter\", \"index\": 0}]]}, \"Embeddings OpenAI1\": {\"ai_embedding\": [[{\"node\": \"Write Document to Pinecone (guideline)\", \"type\": \"ai_embedding\", \"index\": 0}]]}, \"Merge\": {\"main\": [[{\"node\": \"Write Document to Pinecone (guideline)\", \"type\": \"main\", \"index\": 0}]]}, \"Merge1\": {\"main\": [[{\"node\": \"Write document metadata to Supabase\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request: Download genereviews tar.gz file\": {\"main\": [[{\"node\": \"Write genereviews tar.gz file to disk\", \"type\": \"main\", \"index\": 0}]]}, \"Read genereviews files from disk\": {\"main\": [[{\"node\": \"Loop Over genereviews files\", \"type\": \"main\", \"index\": 0}, {\"node\": \"PDF to JSON\", \"type\": \"main\", \"index\": 0}]]}, \"PDF to JSON\": {\"main\": [[{\"node\": \"Capture Original File JSON\", \"type\": \"main\", \"index\": 0}]]}, \"Capture Original File JSON\": {\"main\": [[{\"node\": \"Read Document from Supabase Index\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Merge\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Merge1\", \"type\": \"main\", \"index\": 1}]]}, \"Read Document from Supabase Index\": {\"main\": [[{\"node\": \"Check if document is on Supabase Index\", \"type\": \"main\", \"index\": 0}]]}, \"Check if document is on Supabase Index\": {\"main\": [[], [{\"node\": \"Merge\", \"type\": \"main\", \"index\": 1}]]}, \"Write Document to Pinecone (guideline)\": {\"main\": [[{\"node\": \"Merge1\", \"type\": \"main\", \"index\": 0}]]}, \"Write document metadata to Supabase\": {\"main\": [[{\"node\": \"Loop Over genereviews files\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Update GeneReviews Knowledge Base\", \"nodes\": [{\"parameters\": {}, \"name\": \"Manual Trigger\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"url\": \"https://example.com/gene_reviews.tar.gz\"}, \"name\": \"Download Tar.gz Archive\", \"type\": \"n8n-nodes-base.download\", \"typeVersion\": 1, \"position\": [200, 0]}, {\"parameters\": {\"path\": \"/path/to/extracted/folder\"}, \"name\": \"Extract Tar.gz Archive\", \"type\": \"n8n-nodes-base.extract\", \"typeVersion\": 1, \"position\": [400, 0]}, {\"parameters\": {\"path\": \"/path/to/extracted/folder\"}, \"name\": \"List PDF Files\", \"type\": \"n8n-nodes-base.list\", \"typeVersion\": 1, \"position\": [600, 0]}, {\"parameters\": {\"path\": \"={{ $json.path }}\"}, \"name\": \"Read PDF File\", \"type\": \"n8n-nodes-base.read\", \"typeVersion\": 1, \"position\": [800, 0]}, {\"parameters\": {\"operation\": \"get\", \"table\": \"gene_reviews\", \"id\": \"={{ $json.id }}\"}, \"name\": \"Check Existing Record\", \"type\": \"n8n-nodes-base.supabase\", \"typeVersion\": 1, \"position\": [1000, 0]}, {\"parameters\": {\"condition\": {\"id\": \"={{ $json.id }}\"}}, \"name\": \"Check Record Exists\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 1, \"position\": [1200, 0]}, {\"parameters\": {\"operation\": \"update\", \"table\": \"gene_reviews\", \"id\": \"={{ $json.id }}\", \"metadata\": \"={{ $json.metadata }}\"}, \"name\": \"Update Supabase Metadata\", \"type\": \"n8n-nodes-base.supabase\", \"typeVersion\": 1, \"position\": [1400, 0]}, {\"parameters\": {\"operation\": \"insert\", \"table\": \"gene_reviews\", \"metadata\": \"={{ $json.metadata }}\"}, \"name\": \"Insert New Record\", \"type\": \"n8n-nodes-base.supabase\", \"typeVersion\": 1, \"position\": [1400, 200]}, {\"parameters\": {\"model\": \"text-embedding-ada-002\"}, \"name\": \"Generate Embeddings\", \"type\": \"n8n-nodes-base.openAiEmbeddings\", \"typeVersion\": 1, \"position\": [1600, 200]}, {\"parameters\": {\"chunkSize\": 512}, \"name\": \"Chunk Text\", \"type\": \"n8n-nodes-base.chunk\", \"typeVersion\": 1, \"position\": [1800, 200]}, {\"parameters\": {\"index\": \"gene_reviews\", \"metadata\": \"={{ $json.metadata }}\", \"text\": \"={{ $json.text }}\"}, \"name\": \"Insert into Pinecone\", \"type\": \"n8n-nodes-base.pinecone\", \"typeVersion\": 1, \"position\": [2000, 200]}, {\"parameters\": {\"model\": \"text-embedding-ada-002\"}, \"name\": \"Generate Embeddings\", \"type\": \"n8n-nodes-base.openAiEmbeddings\", \"typeVersion\": 1, \"position\": [1600, 0]}, {\"parameters\": {\"chunkSize\": 512}, \"name\": \"Chunk Text\", \"type\": \"n8n-nodes-base.chunk\", \"typeVersion\": 1, \"position\": [1800, 0]}, {\"parameters\": {\"index\": \"gene_reviews\", \"metadata\": \"={{ $json.metadata }}\", \"text\": \"={{ $json.text }}\"}, \"name\": \"Insert into Pinecone\", \"type\": \"n8n-nodes-base.pinecone\", \"typeVersion\": 1, \"position\": [2000, 0]}], \"connections\": {\"Manual Trigger\": {\"main\": [[{\"node\": \"Download Tar.gz Archive\", \"type\": \"main\", \"index\": 0}]]}, \"Download Tar.gz Archive\": {\"main\": [[{\"node\": \"Extract Tar.gz Archive\", \"type\": \"main\", \"index\": 0}]]}, \"Extract Tar.gz Archive\": {\"main\": [[{\"node\": \"List PDF Files\", \"type\": \"main\", \"index\": 0}]]}, \"List PDF Files\": {\"main\": [[{\"node\": \"Loop Over PDF Files\", \"type\": \"main\", \"index\": 0}]]}, \"Loop Over PDF Files\": {\"main\": [[{\"node\": \"Read PDF File\", \"type\": \"main\", \"index\": 0}]]}, \"Read PDF File\": {\"main\": [[{\"node\": \"Parse PDF File\", \"type\": \"main\", \"index\": 0}]]}, \"Parse PDF File\": {\"main\": [[{\"node\": \"Check Existing Record\", \"type\": \"main\", \"index\": 0}]]}, \"Check Existing Record\": {\"main\": [[{\"node\": \"Check Record Exists\", \"type\": \"main\", \"index\": 0}]]}, \"Check Record Exists\": {\"main\": [[{\"node\": \"Update Supabase Metadata\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Insert New Record\", \"type\": \"main\", \"index\": 0}]]}, \"Insert New Record\": {\"main\": [[{\"node\": \"Generate Embeddings\", \"type\": \"main\", \"index\": 0}]]}, \"Generate Embeddings\": {\"main\": [[{\"node\": \"Chunk Text\", \"type\": \"main\", \"index\": 0}]]}, \"Chunk Text\": {\"main\": [[{\"node\": \"Insert into Pinecone\", \"type\": \"main\", \"index\": 0}]]}, \"Insert into Pinecone\": {\"main\": [[{\"node\": \"Update Supabase Metadata\", \"type\": \"main\", \"index\": 0}]]}, \"Update Supabase Metadata\": {\"main\": [[{\"node\": \"Insert into Pinecone\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Google Maps Email Scraper Template\", \"nodes\": [{\"parameters\": {}, \"name\": \"Remove Duplicate URLs\", \"type\": \"n8n-nodes-base.removeDuplicates\", \"typeVersion\": 1.1, \"position\": [-780, 380]}, {\"parameters\": {\"options\": {}}, \"name\": \"Loop over queries\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [-1080, -100]}, {\"parameters\": {\"url\": \"=https://www.google.com/maps/search/{{ $json.query }}\", \"options\": {\"allowUnauthorizedCerts\": false}}, \"name\": \"Search Google Maps with query\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [-1380, 380]}, {\"parameters\": {\"jsCode\": \"const data = $input.first().json.data\\n\\nconst regex = /https?:\\\\/\\\\/[^\\\\/]+/g\\n\\nconst urls = data.match(regex)\\n\\nreturn urls.map(url => ({json: {url: url}}))\"}, \"name\": \"Scrape URLs from results\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 2, \"position\": [-1180, 380]}, {\"parameters\": {\"options\": {}, \"conditions\": {\"options\": {\"version\": 2, \"leftValue\": \"\", \"caseSensitive\": true, \"typeValidation\": \"strict\"}, \"combinator\": \"and\", \"conditions\": [{\"id\": \"041797f2-2fe2-41dc-902a-d34050b9b304\", \"operator\": {\"type\": \"string\", \"operation\": \"notRegex\"}, \"leftValue\": \"={{ $json.url }}\", \"rightValue\": \"=(google|gstatic|ggpht|schema\\\\.org|example\\\\.com|sentry-next\\\\.wixpress\\\\.com|imli\\\\.com|sentry\\\\.wixpress\\\\.com|ingest\\\\.sentry\\\\.io)\"}, {\"id\": \"eb499a7e-17bc-453c-be08-a47286f726dd\", \"operator\": {\"name\": \"filter.operator.equals\", \"type\": \"string\", \"operation\": \"equals\"}, \"leftValue\": \"\", \"rightValue\": \"\"}]}}, \"name\": \"Filter irrelevant URLs\", \"type\": \"n8n-nodes-base.filter\", \"typeVersion\": 2.2, \"position\": [-980, 380]}, {\"parameters\": {\"url\": \"={{ $json.url }}\", \"options\": {}}, \"name\": \"Request web page for URL\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [-380, 460]}, {\"parameters\": {\"options\": {\"reset\": false}}, \"name\": \"Loop over URLs\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [-580, 380]}, {\"parameters\": {\"options\": {}}, \"name\": \"Loop over pages\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [-360, 120]}, {\"parameters\": {\"mode\": \"runOnceForEachItem\", \"jsCode\": \"const data = $json.data\\n\\nconst emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.(?!png|jpg|gif|jpeg)[a-zA-Z]{2,}/g\\n\\nconst emails = data.match(emailRegex)\\n\\nreturn {json: {emails: emails}}\"}, \"name\": \"Scrape emails from page\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 2, \"position\": [-200, 220]}, {\"parameters\": {\"options\": {\"mergeLists\": true}, \"fieldsToAggregate\": {\"fieldToAggregate\": [{\"fieldToAggregate\": \"emails\"}]}}, \"name\": \"Aggregate arrays of emails\", \"type\": \"n8n-nodes-base.aggregate\", \"typeVersion\": 1, \"position\": [-40, 100]}, {\"parameters\": {\"options\": {}, \"fieldToSplitOut\": \"emails\"}, \"name\": \"Split out into default data structure\", \"type\": \"n8n-nodes-base.splitOut\", \"typeVersion\": 1, \"position\": [180, 100]}, {\"parameters\": {\"compare\": \"selectedFields\", \"options\": {}, \"fieldsToCompare\": \"emails\"}, \"name\": \"Remove duplicate emails\", \"type\": \"n8n-nodes-base.removeDuplicates\", \"typeVersion\": 1.1, \"position\": [400, 100]}, {\"parameters\": {\"options\": {}, \"conditions\": {\"options\": {\"version\": 2, \"leftValue\": \"\", \"caseSensitive\": true, \"typeValidation\": \"strict\"}, \"combinator\": \"and\", \"conditions\": [{\"id\": \"041797f2-2fe2-41dc-902a-d34050b9b304\", \"operator\": {\"type\": \"string\", \"operation\": \"notRegex\"}, \"leftValue\": \"={{ $json.emails }}\", \"rightValue\": \"=(google|gstatic|ggpht|schema\\\\.org|example\\\\.com|sentry\\\\.wixpress\\\\.com|sentry-next\\\\.wixpress\\\\.com|ingest\\\\.sentry\\\\.io|sentry\\\\.io|imli\\\\.com)\"}]}}, \"name\": \"Filter irrelevant emails\", \"type\": \"n8n-nodes-base.filter\", \"typeVersion\": 2.2, \"position\": [600, 100]}, {\"parameters\": {\"columns\": {\"value\": {\"Emails\": \"={{ $json.emails }}\"}, \"schema\": [{\"id\": \"Emails\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"Emails\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}], \"mappingMode\": \"defineBelow\", \"matchingColumns\": [\"Emails\"]}, \"options\": {}, \"operation\": \"append\"}, \"name\": \"Save emails to Google Sheet\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.5, \"position\": [800, 100]}, {\"parameters\": {}, \"name\": \"Starts scraper workflow\", \"type\": \"n8n-nodes-base.executeWorkflowTrigger\", \"typeVersion\": 1, \"position\": [-1600, 380]}, {\"parameters\": {}, \"name\": \"Run workflow\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [-1320, -100]}, {\"parameters\": {\"amount\": 2}, \"name\": \"Wait between executions\", \"type\": \"n8n-nodes-base.wait\", \"typeVersion\": 1.1, \"position\": [-700, 0]}, {\"parameters\": {\"mode\": \"each\", \"options\": {\"waitForSubWorkflow\": false}, \"workflowId\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"={{ $workflow.id }}\"}}, \"name\": \"Execute scraper for query\", \"type\": \"n8n-nodes-base.executeWorkflow\", \"typeVersion\": 1.1, \"position\": [-880, 0]}, {\"parameters\": {\"color\": 5, \"width\": 740, \"height\": 180, \"content\": \"## \\ud83d\\udee0 Setup\\n1. Setup your list of queries in the \\\"Run workflow\\\" manual trigger node. Watch  this [video](https://youtu.be/HaiO-UeiKBA) on how to generate the queries with ChatGPT.\\n3. Choose a sheet to populate with data in the **Google Sheets node**\\n4. Run the workflow and start getting leads into your Google Sheets document\"}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-2280, -140]}, {\"parameters\": {\"color\": 6, \"height\": 100, \"content\": \"**Optional** \\ud83d\\udc47\\nSet wait time between each query workflow execution. Default is 2 seconds.\"}, \"name\": \"Sticky Note2\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-700, -120]}, {\"parameters\": {\"width\": 480, \"height\": 100, \"content\": \"### Scraper \\ud83d\\udc47\\nThis workflow will be executed in the background for each query. Click the **All executions** tab in the left sidebar to see the executions running.\"}, \"name\": \"Sticky Note3\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-1600, 260]}, {\"parameters\": {\"color\": 4, \"height\": 180, \"content\": \"\\ud83d\\udc46 \\n1. Setup your **credentials**. Here's a [video tutorial](https://youtu.be/O5RnWDM27M8) on how to do that.\\n\\n2. Choose which document and sheet to save the scraped emails to. \"}, \"name\": \"Sticky Note4\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [820, 300]}, {\"parameters\": {\"color\": 3, \"content\": \" ## \\u26a0\\ufe0f Note\\n\\nA [video tutorial](https://youtu.be/HaiO-UeiKBA) for this workflow guide is available on my [Youtube channel](https://www.youtube.com/channel/UCn8xmUBunez1SsDVRfZDUGA)\"}, \"name\": \"Sticky Note5\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-1760, -360]}, {\"parameters\": {\"color\": 7, \"width\": 480, \"height\": 140, \"content\": \"## Google Maps Automatic Email Scraper\\n\\nThis workflow automatically scrapes emails from businesses on Google Maps based on a list of queries that you provide.\"}, \"name\": \"Sticky Note6\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-2280, -360]}, {\"parameters\": {\"color\": 6, \"width\": 160, \"height\": 100, \"content\": \"**Optional** \\ud83d\\udc46\\nAdd or change the regex for filtering irrelevant URLs.\"}, \"name\": \"Sticky Note7\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-1000, 540]}, {\"parameters\": {\"color\": 6, \"width\": 200, \"height\": 100, \"content\": \"**Optional** \\ud83d\\udc46\\nAdd or change the regex for filtering irrelevant/incorrect email addresses.\"}, \"name\": \"Sticky Note8\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [580, 260]}], \"connections\": {\"Run workflow\": {\"main\": [[{\"node\": \"Loop over queries\", \"type\": \"main\", \"index\": 0}]]}, \"Loop over URLs\": {\"main\": [[{\"node\": \"Loop over pages\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Request web page for URL\", \"type\": \"main\", \"index\": 0}]]}, \"Loop over pages\": {\"main\": [[{\"node\": \"Aggregate arrays of emails\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Scrape emails from page\", \"type\": \"main\", \"index\": 0}]]}, \"Loop over queries\": {\"main\": [[], [{\"node\": \"Execute scraper for query\", \"type\": \"main\", \"index\": 0}]]}, \"Remove Duplicate URLs\": {\"main\": [[{\"node\": \"Loop over URLs\", \"type\": \"main\", \"index\": 0}]]}, \"Filter irrelevant URLs\": {\"main\": [[{\"node\": \"Remove Duplicate URLs\", \"type\": \"main\", \"index\": 0}]]}, \"Remove duplicate emails\": {\"main\": [[{\"node\": \"Filter irrelevant emails\", \"type\": \"main\", \"index\": 0}]]}, \"Scrape emails from page\": {\"main\": [[{\"node\": \"Loop over pages\", \"type\": \"main\", \"index\": 0}]]}, \"Starts scraper workflow\": {\"main\": [[{\"node\": \"Search Google Maps with query\", \"type\": \"main\", \"index\": 0}]]}, \"Wait between executions\": {\"main\": [[{\"node\": \"Loop over queries\", \"type\": \"main\", \"index\": 0}]]}, \"Filter irrelevant emails\": {\"main\": [[{\"node\": \"Save emails to Google Sheet\", \"type\": \"main\", \"index\": 0}]]}, \"Request web page for URL\": {\"main\": [[{\"node\": \"Loop over URLs\", \"type\": \"main\", \"index\": 0}]]}, \"Scrape URLs from results\": {\"main\": [[{\"node\": \"Filter irrelevant URLs\", \"type\": \"main\", \"index\": 0}]]}, \"Execute scraper for query\": {\"main\": [[{\"node\": \"Wait between executions\", \"type\": \"main\", \"index\": 0}]]}, \"Aggregate arrays of emails\": {\"main\": [[{\"node\": \"Split out into default data structure\", \"type\": \"main\", \"index\": 0}]]}, \"Search Google Maps with query\": {\"main\": [[{\"node\": \"Scrape URLs from results\", \"type\": \"main\", \"index\": 0}]]}, \"Split out into default data structure\": {\"main\": [[{\"node\": \"Remove duplicate emails\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"url\": \"https://api.imgbb.com/1/upload\", \"method\": \"POST\", \"options\": {}, \"sendBody\": true, \"contentType\": \"multipart-form-data\", \"sendHeaders\": true, \"authentication\": \"genericCredentialType\", \"bodyParameters\": {\"parameters\": [{\"name\": \"image\", \"parameterType\": \"formBinaryData\", \"inputDataFieldName\": \"data\"}]}, \"genericAuthType\": \"httpQueryAuth\", \"headerParameters\": {\"parameters\": [{\"name\": \"Content-type\", \"value\": \"multipart/form-data\"}]}}, \"name\": \"Upload Img to ImgBB for URL\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [120, 6220]}, {\"parameters\": {\"url\": \"=http://api.resmush.it/ws.php?img={{ $json.data.url }}\", \"options\": {}}, \"name\": \"ReSmush.it Image Optimisation\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [320, 6220]}, {\"parameters\": {\"url\": \"https://api.imgbb.com/1/upload\", \"method\": \"POST\", \"options\": {}, \"sendBody\": true, \"sendHeaders\": true, \"authentication\": \"genericCredentialType\", \"bodyParameters\": {\"parameters\": [{\"name\": \"image\", \"value\": \"={{ $json.dest }}\"}]}, \"genericAuthType\": \"httpQueryAuth\", \"headerParameters\": {\"parameters\": [{\"name\": \"Content-type\", \"value\": \"application/x-www-form-urlencoded\"}]}}, \"name\": \"Store Optimised Image ImgBB\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [540, 6220]}, {\"parameters\": {\"color\": 7, \"width\": 415.48118604428106, \"height\": 320.9196076003899, \"content\": \"**Image Prompt**\\n\\nPrompt takes input of image description from the `set image description` node and generates using OpenAI\"}, \"name\": \"Sticky Note50\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-343.4815115846739, 6060]}, {\"parameters\": {\"color\": 7, \"width\": 619.0692735087202, \"height\": 320.9196076003899, \"content\": \"**Upload image to ImgBB, Optimise using ReSmush.it and store as URL**\\n\"}, \"name\": \"Sticky Note51\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [80, 6060]}, {\"parameters\": {\"color\": 4, \"width\": 773.6179704580734, \"height\": 875.8289847608302, \"content\": \"## Convert Image Files (JPG, PNG, JPEG) to URLs and Reduce File Size\\n\\n## Use Case\\nTransform and optimize images for web use:\\n- You need to host local images online\\n- You want to reduce image file sizes automatically\\n- You need image URLs for web content\\n- You want to generate and optimize AI-created images\\n\\n## What this Workflow Does\\nThe workflow processes images through two services:\\n- Uploads images to ImgBB for hosting and URL generation (free but need API key)\\n- Optimizes images using ReSmush.it to reduce file size (free)\\n- Optional: Creates images using OpenAI's image generation\\n- Returns optimized image URLs ready for use\\n\\n## Setup\\n1. Create an [ImgBB account](https://api.imgbb.com/) and get your API key\\n2. Add your ImgBB API key to the HTTP Request node (key parameter)\\n3. Optional: Configure OpenAI credentials for image generation\\n4. Connect your image input source\\n\\n## How to Adjust it to Your Needs\\n- Skip OpenAI nodes if using your own image files\\n- Adjust image optimization parameters\\n- Customize image hosting settings\\n- Modify output format for your needs\\n\\n\\nMade by Simon @ [automake.io](https://automake.io)\"}, \"name\": \"Sticky Note52\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-1160, 6020]}, {\"parameters\": {\"color\": 3, \"width\": 620.*************, \"height\": 218.**************, \"content\": \"**REQUIRED**\\n\\n**ImgBB - image hosting i.e. gives you an img url**\\n1. [Create an ImgBB account](https://api.imgbb.com/) (free) and generate an api key\\n2. Input the API key as Query Auth - `name`=key, `value`=your-own-api-key\\n\\n\\n**ReSmush.it - image optimisation i.e. shrinks the file size of the image**\\n1. No account or auth needed\\n2. Url will pass from previous node\"}, \"name\": \"Sticky Note53\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [80, 6400]}, {\"parameters\": {\"color\": 2, \"width\": 409.*************, \"height\": 133.**************, \"content\": \"**OPTIONAL**\\n`Set image description` to create an Image using OpenAI and your own prompt (requires: API credentials) or alternatively replace these nodes with your own image file\"}, \"name\": \"Sticky Note54\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-340, 6400]}, {\"parameters\": {\"options\": {}, \"assignments\": {\"assignments\": [{\"id\": \"9026b5d5-97ed-484e-a168-ac1c57a60fa1\", \"name\": \"description\", \"type\": \"string\", \"value\": \"=Balancing Autonomy and Human Interaction in AI Applications, featuring a person\"}]}}, \"name\": \"Set image description\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [-300, 6220]}, {\"parameters\": {\"prompt\": \"=Create a minimalist professional illustration of {{ $json.description }} with these specifications:\\n\\n1. Visual Style:\\n- Modern tech-focused minimalist design\\n- Clean, uncluttered composition\\n- Professional business aesthetic\\n- Soft shadows and subtle depth\\n- 2-3 primary colors maximum plus white space\\n\\n2. Core Elements:\\n- Main icon/symbol representing {{ $json.description }} as focal point\\n- Simple supporting elements representing key sections\\n- Subtle connecting elements showing relationship\\n- Plenty of white space (40% minimum)\\n- No text overlay\\n\\n3. Technical Requirements:\\n- High contrast for clarity\\n- Crisp edges and smooth lines\\n- Professional lighting from upper left\\n- Matte finish\\n- Square aspect ratio (1:1)\", \"options\": {}, \"resource\": \"image\"}, \"name\": \"Generate Image\", \"type\": \"@n8n/n8n-nodes-langchain.openAi\", \"typeVersion\": 1.4, \"position\": [-100, 6220]}, {\"parameters\": {}, \"name\": \"No Operation, do nothing\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [760, 6220]}], \"connections\": {\"Generate Image\": {\"main\": [[{\"node\": \"Upload Img to ImgBB for URL\", \"type\": \"main\", \"index\": 0}]]}, \"Set image description\": {\"main\": [[{\"node\": \"Generate Image\", \"type\": \"main\", \"index\": 0}]]}, \"Store Optimised Image ImgBB\": {\"main\": [[{\"node\": \"No Operation, do nothing\", \"type\": \"main\", \"index\": 0}]]}, \"Upload Img to ImgBB for URL\": {\"main\": [[{\"node\": \"ReSmush.it Image Optimisation\", \"type\": \"main\", \"index\": 0}]]}, \"ReSmush.it Image Optimisation\": {\"main\": [[{\"node\": \"Store Optimised Image ImgBB\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Generate image from description, optimize it and upload to imgbb\", \"nodes\": [{\"parameters\": {\"model\": \"dall-e-3\", \"prompt\": \"A beautiful landscape\", \"size\": \"1024x1024\", \"responseFormat\": \"b64_json\"}, \"name\": \"dall-e-3\", \"type\": \"n8n-nodes-base.openAi\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"data\": \"={{ $json.data.base64 }}\"}, \"name\": \"base64-to-binary\", \"type\": \"n8n-nodes-base.base64ToBinary\", \"typeVersion\": 1, \"position\": [220, 0]}, {\"parameters\": {\"url\": \"https://api.imgbb.com/1/upload\", \"method\": \"POST\", \"sendBody\": true, \"sendBinaryData\": true, \"queryParameters\": {\"key\": \"YOUR_IMGBB_API_KEY\"}}, \"name\": \"imgbb-upload\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 1, \"position\": [440, 0]}, {\"parameters\": {\"url\": \"https://api.resmush.it/ws.php\", \"method\": \"POST\", \"sendBody\": true, \"sendQuery\": true, \"queryParameters\": {\"action\": \"smush\", \"url\": \"={{ $json.url }}\"}}, \"name\": \"resmush\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 1, \"position\": [660, 0]}, {\"parameters\": {\"url\": \"={{ $json.smush.url }}\", \"responseFormat\": \"binary\"}, \"name\": \"download-optimized\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 1, \"position\": [880, 0]}, {\"parameters\": {\"url\": \"https://api.imgbb.com/1/upload\", \"method\": \"POST\", \"sendBody\": true, \"sendBinaryData\": true, \"queryParameters\": {\"key\": \"YOUR_IMGBB_API_KEY\"}}, \"name\": \"imgbb-upload-optimized\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 1, \"position\": [1100, 0]}], \"connections\": {\"dall-e-3\": {\"main\": [[{\"node\": \"base64-to-binary\", \"type\": \"main\", \"index\": 0}]]}, \"base64-to-binary\": {\"main\": [[{\"node\": \"imgbb-upload\", \"type\": \"main\", \"index\": 0}]]}, \"imgbb-upload\": {\"main\": [[{\"node\": \"resmush\", \"type\": \"main\", \"index\": 0}]]}, \"resmush\": {\"main\": [[{\"node\": \"download-optimized\", \"type\": \"main\", \"index\": 0}]]}, \"download-optimized\": {\"main\": [[{\"node\": \"imgbb-upload-optimized\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"options\": {}, \"assignments\": {\"assignments\": [{\"id\": \"b836ba12-262a-4fed-a31d-9e2f6514137a\", \"name\": \"startUrls\", \"type\": \"array\", \"value\": \"=[\\n    {\\n      \\\"url\\\": \\\"https://www.upwork.com/nx/search/jobs/?nbs=1&q=python\\\",\\n      \\\"method\\\": \\\"GET\\\"\\n    },\\n{\\n            \\\"url\\\": \\\"https://www.upwork.com/nx/search/jobs/?nbs=1&q=java\\\",\\n            \\\"method\\\": \\\"GET\\\"\\n        }\\n  ]\"}, {\"id\": \"5f7ba5cc-a8fc-4f67-9feb-6243d08462f9\", \"name\": \"proxyCountryCode\", \"type\": \"string\", \"value\": \"FR\"}]}}, \"name\": \"Assign parameters\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [300, 80]}, {\"parameters\": {\"url\": \"=https://api.apify.com/v2/acts/arlusm~upwork-scraper-with-fresh-job-posts/run-sync-get-dataset-items\", \"method\": \"POST\", \"options\": {}, \"sendBody\": true, \"authentication\": \"genericCredentialType\", \"bodyParameters\": {\"parameters\": [{\"name\": \"startUrls\", \"value\": \"={{ $json.startUrls }}\"}, {\"name\": \"proxyCountryCode\", \"value\": \"={{ $json.proxyCountryCode }}\"}]}, \"genericAuthType\": \"httpQueryAuth\"}, \"name\": \"Query For Upwork Job Posts\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [520, 80]}, {\"parameters\": {\"rule\": {\"interval\": [{\"field\": \"minutes\", \"minutesInterval\": 10}]}}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.2, \"position\": [-100, 80]}, {\"parameters\": {\"options\": {}, \"conditions\": {\"options\": {\"version\": 2, \"leftValue\": \"\", \"caseSensitive\": true, \"typeValidation\": \"loose\"}, \"combinator\": \"and\", \"conditions\": [{\"id\": \"795a6d51-0ea0-4493-bc1e-a1807a2cbd77\", \"operator\": {\"type\": \"number\", \"operation\": \"gt\"}, \"leftValue\": \"={{ $json.Hour }}\", \"rightValue\": 2}, {\"id\": \"f9ba101d-226d-4d6a-aab8-62229762a046\", \"operator\": {\"type\": \"number\", \"operation\": \"lt\"}, \"leftValue\": \"={{ $json.Hour }}\", \"rightValue\": 15}]}, \"looseTypeValidation\": true}, \"name\": \"If Working Hours\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2.2, \"position\": [80, 80]}, {\"parameters\": {\"query\": \"={\\n  \\\"title\\\": \\\"{{ $json.title }}\\\",\\n  \\\"budget\\\": \\\"{{ $json.budget }}\\\"\\n}\\n\", \"options\": {}, \"collection\": \"n8n\"}, \"name\": \"Find Existing Entries\", \"type\": \"n8n-nodes-base.mongoDb\", \"typeVersion\": 1.1, \"position\": [720, -40]}, {\"parameters\": {\"mode\": \"combine\", \"options\": {}, \"joinMode\": \"keepNonMatches\", \"fieldsToMatchString\": \"title, budget\"}, \"name\": \"Output New Entries\", \"type\": \"n8n-nodes-base.merge\", \"typeVersion\": 3, \"position\": [940, 80]}, {\"parameters\": {\"fields\": \"title,link,paymentType,budget,projectLength,shortBio,skills,publishedDate,normalizedDate,searchUrl\", \"options\": {}, \"operation\": \"insert\", \"collection\": \"n8n\"}, \"name\": \"Add New Entries To MongoDB\", \"type\": \"n8n-nodes-base.mongoDb\", \"typeVersion\": 1.1, \"position\": [1160, -40]}, {\"parameters\": {\"height\": 260, \"content\": \"## Setup\\n1. Add MongoDB, Slack credentials\\n2. Add a query auth credential where the key='token' and the value being your apify token\\n3. Modify the 'Assign parameters' node to include the Upwork URLs you want to query for\"}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [220, -240]}, {\"parameters\": {\"text\": \"=Job Title : {{ $json.title }}\\nPublished : {{ $json.publishedDate }}\\nLink : {{ $json.link }}\\nPayment Type: {{ $json.paymentType }}\\nBudget: {{ $json.budget }}\\nSkills: {{ $json.skills }}\\nBio: {{ $json.shortBio }}\", \"select\": \"channel\", \"channelId\": {\"__rl\": true, \"mode\": \"name\", \"value\": \"#general\"}, \"otherOptions\": {}}, \"name\": \"Send message in #general\", \"type\": \"n8n-nodes-base.slack\", \"typeVersion\": 2.3, \"position\": [1160, 200]}], \"connections\": {\"If Working Hours\": {\"main\": [[{\"node\": \"Assign parameters\", \"type\": \"main\", \"index\": 0}]]}, \"Schedule Trigger\": {\"main\": [[{\"node\": \"If Working Hours\", \"type\": \"main\", \"index\": 0}]]}, \"Assign parameters\": {\"main\": [[{\"node\": \"Query For Upwork Job Posts\", \"type\": \"main\", \"index\": 0}]]}, \"Output New Entries\": {\"main\": [[{\"node\": \"Add New Entries To MongoDB\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Send message in #general\", \"type\": \"main\", \"index\": 0}]]}, \"Find Existing Entries\": {\"main\": [[{\"node\": \"Output New Entries\", \"type\": \"main\", \"index\": 0}]]}, \"Query For Upwork Job Posts\": {\"main\": [[{\"node\": \"Find Existing Entries\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Output New Entries\", \"type\": \"main\", \"index\": 1}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"someonegao rss to tg\", \"nodes\": [{\"parameters\": {}, \"name\": \"Start\", \"type\": \"n8n-nodes-base.start\", \"typeVersion\": 1, \"position\": [-160, 460]}, {\"parameters\": {\"url\": \"https://someonegao.com/rss.xml\"}, \"name\": \"RSS Feed Read\", \"type\": \"n8n-nodes-base.rssFeedRead\", \"typeVersion\": 1, \"position\": [80, 460]}, {\"parameters\": {\"functionCode\": \"/* only return new items */\\nconst staticData = getWorkflowStaticData('global');\\nconst lastItemId = staticData.lastItemId;\\n\\nconsole.log('lastItemId', lastItemId);\\nconst firstItem = items[0];\\nlet newItems = [];\\n\\nfunction getId(item) {\\n  return item.json.guid;\\n}\\n\\nif (lastItemId) {\\n  for (const item of items) {\\n    if (getId(item) === lastItemId) {\\n      break;\\n    }\\n    newItems.push(item)\\n  }\\n} else {\\n  newItems = [firstItem]\\n}\\n\\nstaticData.lastItemId = getId(firstItem)\\nreturn newItems.reverse()\"}, \"name\": \"Function\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [280, 460]}, {\"parameters\": {\"chatId\": \"@reorx_share\", \"text\": \"=\\ud83d\\udcd6 {{$node[\\\"RSS Feed Read\\\"].json[\\\"title\\\"]}} #repost #someonegao\\n\\n{{$node[\\\"RSS Feed Read\\\"].json[\\\"link\\\"]}}\", \"additionalFields\": {\"parse_mode\": \"HTML\"}}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1, \"position\": [500, 460]}, {\"parameters\": {\"unit\": \"hours\"}, \"name\": \"Interval\", \"type\": \"n8n-nodes-base.interval\", \"typeVersion\": 1, \"position\": [-160, 640]}], \"connections\": {\"RSS Feed Read\": {\"main\": [[{\"node\": \"Function\", \"type\": \"main\", \"index\": 0}]]}, \"Function\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}, \"Start\": {\"main\": [[{\"node\": \"RSS Feed Read\", \"type\": \"main\", \"index\": 0}]]}, \"Interval\": {\"main\": [[{\"node\": \"RSS Feed Read\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"RSS to Telegram\", \"nodes\": [{\"parameters\": {\"cronExpression\": \"0 * * * *\"}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"url\": \"https://someonegao.com/rss.xml\", \"update\": true}, \"name\": \"RSS Feed Read\", \"type\": \"n8n-nodes-base.rssFeedRead\", \"typeVersion\": 1, \"position\": [220, 0]}, {\"parameters\": {\"functionCode\": \"const lastGuid = $getWorkflowStaticData('lastGuid');\\nconst newItems = [];\\nfor (const item of items) {\\n  if (item.guid === lastGuid) {\\n    break; // Skip duplicates\\n  }\\n  newItems.push(item);\\n}\\n\\n// Update the last GUID with the new item's GUID\\nif (newItems.length > 0) {\\n  $setWorkflowStaticData('lastGuid', newItems[0].guid);\\n}\\n\\nreturn newItems;\"}, \"name\": \"Function\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [440, 0]}, {\"parameters\": {\"chatId\": \"@reorx_share\", \"text\": \"=New post: {{ $json.title }}\\n{{ $json.link }}\", \"parseMode\": \"HTML\"}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1, \"position\": [660, 0]}, {\"parameters\": {\"chatId\": \"@reorx_share\", \"text\": \"=Error occurred in workflow: {{ $json.error.message }}\", \"parseMode\": \"HTML\"}, \"name\": \"Telegram1\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1, \"position\": [220, 220]}], \"connections\": {\"Schedule Trigger\": {\"main\": [[{\"node\": \"RSS Feed Read\", \"type\": \"main\", \"index\": 0}]]}, \"RSS Feed Read\": {\"main\": [[{\"node\": \"Function\", \"type\": \"main\", \"index\": 0}]]}, \"Function\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}, \"Schedule Trigger\": {\"onError\": [[{\"node\": \"Telegram1\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {}, \"name\": \"When clicking \\u2018Test workflow\\u2019\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [420, 220]}, {\"parameters\": {\"fields\": {\"values\": [{\"name\": \"body\", \"stringValue\": \"<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?> <library>     <book>         <title>Introduction to XML</title>         <author><PERSON></author>         <publication_year>2020</publication_year>         <isbn>1234567890</isbn>     </book>     <book>         <title>Data Science Basics</title>         <author><PERSON></author>         <publication_year>2019</publication_year>         <isbn>0987654321</isbn>     </book>     <book>         <title>Programming in Python</title>         <author><PERSON></author>         <publication_year>2021</publication_year>         <isbn>5432109876</isbn>     </book> </library>\"}]}, \"options\": {}}, \"name\": \"Edit Fields\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [640, 220]}], \"connections\": {\"When clicking \\u2018Test workflow\\u2019\": {\"main\": [[{\"node\": \"Edit Fields\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Multi Book XML Library\", \"nodes\": [{\"parameters\": {}, \"name\": \"When clicking \\u2018Test workflow\\u2019\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [250, 300]}, {\"parameters\": {\"body\": \"<library><book><title><PERSON> and the Philosopher's Stone</title><author><PERSON><PERSON><PERSON><PERSON></author><year>1997</year></book><book><title><PERSON> and the Chamber of Secrets</title><author><PERSON><PERSON><PERSON><PERSON></author><year>1998</year></book><book><title><PERSON> and the Prisoner of Azkaban</title><author><PERSON><PERSON><PERSON><PERSON></author><year>1999</year></book><book><title><PERSON> and the Goblet of Fire</title><author><PERSON>.<PERSON><PERSON></author><year>2000</year></book><book><title><PERSON> and the Order of the Phoenix</title><author><PERSON><PERSON><PERSON><PERSON></author><year>2003</year></book><book><title><PERSON> and the Half-Blood Prince</title><author><PERSON><PERSON><PERSON><PERSON></author><year>2005</year></book><book><title><PERSON> and the Deathly Hallows</title><author>J.<PERSON>. <PERSON>ling</author><year>2007</year></book></library>\"}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [450, 300]}], \"connections\": {\"When clicking \\u2018Test workflow\\u2019\": {\"main\": [[{\"node\": \"Set\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Schedule + pinned\", \"nodes\": [{\"parameters\": {\"rule\": {\"interval\": [{}]}}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.1, \"position\": [660, 340]}, {\"parameters\": {\"options\": {}}, \"name\": \"Edit Fields\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [860, 240]}, {\"parameters\": {\"options\": {}}, \"name\": \"Edit Fields1\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [860, 440]}, {\"parameters\": {\"options\": {}}, \"name\": \"Edit Fields2\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1080, 440]}, {\"parameters\": {\"options\": {}}, \"name\": \"Edit Fields3\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1300, 440]}, {\"parameters\": {\"options\": {}}, \"name\": \"Edit Fields4\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1500, 440]}, {\"parameters\": {\"options\": {}}, \"name\": \"Edit Fields5\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1080, 240]}, {\"parameters\": {\"options\": {}}, \"name\": \"Edit Fields6\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1300, 240]}, {\"parameters\": {\"options\": {}}, \"name\": \"Edit Fields7\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1500, 240]}, {\"parameters\": {\"options\": {}}, \"name\": \"Edit Fields9\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1700, 240]}, {\"parameters\": {\"options\": {}}, \"name\": \"Edit Fields10\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1700, 440]}], \"connections\": {\"Schedule Trigger\": {\"main\": [[{\"node\": \"Edit Fields\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Edit Fields1\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields1\": {\"main\": [[{\"node\": \"Edit Fields2\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields2\": {\"main\": [[{\"node\": \"Edit Fields3\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields3\": {\"main\": [[{\"node\": \"Edit Fields4\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields5\": {\"main\": [[{\"node\": \"Edit Fields6\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields6\": {\"main\": [[{\"node\": \"Edit Fields7\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields\": {\"main\": [[{\"node\": \"Edit Fields5\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields7\": {\"main\": [[{\"node\": \"Edit Fields9\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields4\": {\"main\": [[{\"node\": \"Edit Fields10\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Schedule Trigger with parallel sequences of Set nodes\", \"nodes\": [{\"parameters\": {\"set\": {}}, \"name\": \"Edit Fields7\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [500, 300]}, {\"parameters\": {\"set\": {}}, \"name\": \"Edit Fields2\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [500, 500]}, {\"parameters\": {}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1, \"position\": [300, 400]}], \"connections\": {\"Schedule Trigger\": {\"main\": [[{\"node\": \"Edit Fields7\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Edit Fields2\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Automate Content Generator for WordPress with DeepSeek R1\", \"nodes\": [{\"parameters\": {}, \"name\": \"When clicking \\u2018Test workflow\\u2019\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [-20, 200]}, {\"parameters\": {\"options\": {}, \"sheetName\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"=Sheet1\"}, \"documentId\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"YOURDOCUMENT\"}}, \"name\": \"Get Ideas\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.5, \"position\": [200, 200]}, {\"parameters\": {\"options\": {}, \"assignments\": {\"assignments\": [{\"id\": \"3e8d2523-66aa-46fe-adcc-39dc78b9161e\", \"name\": \"prompt\", \"type\": \"string\", \"value\": \"={{ $json.PROMPT }}\"}]}}, \"name\": \"Set your prompt\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [420, 200]}, {\"parameters\": {\"title\": \"={{ $json.message.content }}\", \"additionalFields\": {\"status\": \"draft\", \"content\": \"={{ $('Generate article with DeepSeek').item.json.message.content }}\"}}, \"name\": \"Create post on Wordpress\", \"type\": \"n8n-nodes-base.wordpress\", \"typeVersion\": 1, \"position\": [0, 500]}, {\"parameters\": {\"url\": \"https://YOURSITE.com/wp-json/wp/v2/media\", \"method\": \"POST\", \"options\": {}, \"sendBody\": true, \"contentType\": \"binaryData\", \"sendHeaders\": true, \"authentication\": \"predefinedCredentialType\", \"headerParameters\": {\"parameters\": [{\"name\": \"Content-Disposition\", \"value\": \"=attachment; filename=\\\"copertina-{{ $('Create post on Wordpress').item.json.id }}.jpg\\\"\"}]}, \"inputDataFieldName\": \"data\", \"nodeCredentialType\": \"wordpressApi\"}, \"name\": \"Upload image\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [420, 500]}, {\"parameters\": {\"url\": \"=https://wp.test.7hype.com/wp-json/wp/v2/posts/{{ $('Create post on Wordpress').item.json.id }}\", \"method\": \"POST\", \"options\": {}, \"sendQuery\": true, \"authentication\": \"predefinedCredentialType\", \"queryParameters\": {\"parameters\": [{\"name\": \"featured_media\", \"value\": \"={{ $json.id }}\"}]}, \"nodeCredentialType\": \"wordpressApi\"}, \"name\": \"Set Image\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [640, 500]}, {\"parameters\": {\"columns\": {\"value\": {\"DATA\": \"={{ $now.format('dd/LL/yyyy') }}\", \"TITOLO\": \"={{ $('Generate title with DeepSeek').item.json.message.content }}\", \"ID POST\": \"={{ $('Create post on Wordpress').item.json.id }}\", \"row_number\": \"={{ $('Get Ideas').item.json.row_number }}\"}, \"schema\": [{\"id\": \"DATA\", \"type\": \"string\", \"display\": true, \"required\": false, \"displayName\": \"DATA\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"PROMPT\", \"type\": \"string\", \"display\": true, \"required\": false, \"displayName\": \"PROMPT\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"TITOLO\", \"type\": \"string\", \"display\": true, \"required\": false, \"displayName\": \"TITOLO\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"ID POST\", \"type\": \"string\", \"display\": true, \"required\": false, \"displayName\": \"ID POST\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"row_number\", \"type\": \"string\", \"display\": true, \"removed\": false, \"readOnly\": true, \"required\": false, \"displayName\": \"row_number\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}], \"mappingMode\": \"defineBelow\", \"matchingColumns\": [\"row_number\"], \"attemptToConvertTypes\": false, \"convertFieldsToString\": false}, \"options\": {}, \"operation\": \"update\", \"sheetName\": {\"__rl\": true, \"mode\": \"list\", \"value\": \"gid=0\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/16VFeCrE5BkMBoA_S5HD-9v7C0sxcXAUiDbq5JvkDqnI/edit#gid=0\", \"cachedResultName\": \"Foglio1\"}, \"documentId\": {\"__rl\": true, \"mode\": \"list\", \"value\": \"16VFeCrE5BkMBoA_S5HD-9v7C0sxcXAUiDbq5JvkDqnI\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/16VFeCrE5BkMBoA_S5HD-9v7C0sxcXAUiDbq5JvkDqnI/edit?usp=drivesdk\", \"cachedResultName\": \"Plan Blog wp.test.7hype.com\"}}, \"name\": \"Update Sheet\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.5, \"position\": [880, 500]}, {\"parameters\": {\"color\": 3, \"width\": 800, \"height\": 380, \"content\": \"## Target\\nThis workflow is designed to automatically generate seo-friendly content for wordpress through DeepSeek R1 by giving input ideas on how to structure the article. A cover image is also generated and uploaded with OpenAI DALL-E 3. This flow is designed to be executed automatically (ad \\\"On a schedule\\\" node) and thus have a complete editorial plan.\\n\\nThis process is useful for blog managers who want to automate content creation and publishing.\\n\\n## Preliminary step\\nCreate a google sheet with the following columns:\\n- Date\\n- Prompt\\n- Title\\n- Post ID\\n\\nFill in only the \\\"Prompt\\\" column with basic ideas that DeepSeek will work on to generate the content.\"}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-60, -360]}, {\"parameters\": {\"height\": 260, \"content\": \"Connect with your Google Sheet. This node select only rows for which no content has been generated yet in WordPress\"}, \"name\": \"Sticky Note1\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [140, 100]}, {\"parameters\": {\"modelId\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"=deepseek-reasoner\"}, \"options\": {\"maxTokens\": 2048}, \"messages\": {\"values\": [{\"content\": \"=You are an SEO expert, write an article based on this topic:\\n{{ $json.prompt }}\\n\\nInstructions:\\n- In the introduction, introduce the topic that will be explored in the rest of the text\\n- The introduction should be about 120 words\\n- The conclusions should be about 120 words\\n- Use the conclusions to summarize everything said in the article and offer a conclusion to the reader\\n- Write a maximum of 4-5 chapters and argue them.\\n- The chapters should follow a logical flow and not repeat the same concepts.\\n- The chapters should be related to each other and not isolated blocks of text. The text should flow and follow a linear logic.\\n- Do not start chapters with \\\"Chapter 1\\\", \\\"Chapter 2\\\", \\\"Chapter 3\\\" ... write only the chapter title\\n- For the text, use HTML for formatting, but limit yourself to bold, italics, paragraphs and lists.\\n- Don't put the output in ```html but only text\\n- Don't use markdown for formatting.\\n- Go deeper into the topic you're talking about, don't just throw superficial information there\\n- In output I want only the HTML format\"}]}}, \"name\": \"Generate article with DeepSeek\", \"type\": \"@n8n/n8n-nodes-langchain.openAi\", \"typeVersion\": 1.8, \"position\": [640, 200]}, {\"parameters\": {\"modelId\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"=deepseek-reasoner\"}, \"options\": {\"maxTokens\": 2048}, \"messages\": {\"values\": [{\"content\": \"=You are an SEO Copywriter and you need to think of a title of maximum 60 characters for the following article:\\n{{ $json.message.content }}\\n\\nInstructions:\\n- Use keywords contained in the article\\n- Do not use any HTML characters\\n- Output only the string containing the title.\\n- Do not use quotation marks. The only special characters allowed are \\\":\\\" and \\\",\\\"\"}]}}, \"name\": \"Generate title with DeepSeek\", \"type\": \"@n8n/n8n-nodes-langchain.openAi\", \"typeVersion\": 1.8, \"position\": [880, 200]}, {\"parameters\": {\"width\": 420, \"height\": 260, \"content\": \"Add your DeepSeek API credential. If you want you can change the model with \\\"deepseek-chat\\\"\"}, \"name\": \"Sticky Note2\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [580, 100]}, {\"parameters\": {\"width\": 160, \"height\": 260, \"content\": \"Add your WordPress API credential\\n\"}, \"name\": \"Sticky Note3\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-20, 420]}, {\"parameters\": {\"prompt\": \"=Generate a real photographic image used as a cover for a blog post:\\n\\nImage prompt:\\n{{ $('Generate title with DeepSeek').item.json.message.content }}, photography, realistic, sigma 85mm f/1.4\", \"options\": {\"size\": \"1792x1024\", \"style\": \"natural\", \"quality\": \"hd\"}, \"resource\": \"image\"}, \"name\": \"Generate Image with DALL-E\", \"type\": \"@n8n/n8n-nodes-langchain.openAi\", \"typeVersion\": 1.8, \"position\": [200, 500]}, {\"parameters\": {\"width\": 160, \"height\": 260, \"content\": \"Add your OpenAI API credential\\n\"}, \"name\": \"Sticky Note4\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [180, 420]}, {\"parameters\": {\"width\": 180, \"height\": 260, \"content\": \"Upload the image on your WordPress via APIs\\n\"}, \"name\": \"Sticky Note5\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [380, 420]}, {\"parameters\": {\"width\": 180, \"height\": 260, \"content\": \"Set the uploaded image with the newly created article\\n\"}, \"name\": \"Sticky Note6\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [600, 420]}], \"connections\": {\"Get Ideas\": {\"main\": [[{\"node\": \"Set your prompt\", \"type\": \"main\", \"index\": 0}]]}, \"Set Image\": {\"main\": [[{\"node\": \"Update Sheet\", \"type\": \"main\", \"index\": 0}]]}, \"Upload image\": {\"main\": [[{\"node\": \"Set Image\", \"type\": \"main\", \"index\": 0}]]}, \"Set your prompt\": {\"main\": [[{\"node\": \"Generate article with DeepSeek\", \"type\": \"main\", \"index\": 0}]]}, \"Create post on Wordpress\": {\"main\": [[{\"node\": \"Generate Image with DALL-E\", \"type\": \"main\", \"index\": 0}]]}, \"Generate Image with DALL-E\": {\"main\": [[{\"node\": \"Upload image\", \"type\": \"main\", \"index\": 0}]]}, \"Generate title with DeepSeek\": {\"main\": [[{\"node\": \"Create post on Wordpress\", \"type\": \"main\", \"index\": 0}]]}, \"Generate article with DeepSeek\": {\"main\": [[{\"node\": \"Generate title with DeepSeek\", \"type\": \"main\", \"index\": 0}]]}, \"When clicking \\u2018Test workflow\\u2019\": {\"main\": [[{\"node\": \"Get Ideas\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"WAHA - Custom HTTP API Request\", \"nodes\": [{\"parameters\": {}, \"name\": \"WAHA Trigger\", \"type\": \"@devlikeapro/n8n-nodes-waha.wahaTrigger\", \"typeVersion\": 202409, \"position\": [1380, 860]}, {\"parameters\": {\"content\": \"## Send Video via HTTP Request on \\\"video\\\"\", \"height\": 339.6993458980035, \"width\": 568.1572184210546, \"color\": 4}, \"name\": \"Sticky Note8\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [1840, 980]}, {\"parameters\": {\"method\": \"POST\", \"url\": \"http://localhost:3000/api/sendVideo\", \"authentication\": \"predefinedCredentialType\", \"nodeCredentialType\": \"wahaApi\", \"sendBody\": true, \"specifyBody\": \"json\", \"jsonBody\": \"={\\n  \\\"session\\\": \\\"{{ $json.session }}\\\",\\n  \\\"chatId\\\": \\\"{{ $json.payload.from }}\\\",\\n  \\\"caption\\\": \\\"Watch this video!\\\",\\n  \\\"file\\\": {\\n    \\\"mimetype\\\": \\\"video/mp4\\\",\\n    \\\"filename\\\": \\\"video.mp4\\\",\\n    \\\"url\\\": \\\"https://github.com/devlikeapro/waha/raw/core/examples/video.mp4\\\"\\n  }\\n}\", \"options\": {}}, \"name\": \"HTTP Request - WAHA Send Video\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [2140, 1060]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"id\": \"f827f7f8-d40c-4d97-9677-9e62d99c7e7a\", \"leftValue\": \"={{ $json.payload.body }}\", \"rightValue\": \"video\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\"}}, {\"id\": \"9dc60cdc-5d7b-44ab-b959-3cec77632855\", \"leftValue\": \"={{ $json.payload.body }}\", \"rightValue\": \"Video\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\", \"name\": \"filter.operator.equals\"}}], \"combinator\": \"or\"}, \"options\": {}}, \"name\": \"body=video\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2.1, \"position\": [1900, 1080]}, {\"parameters\": {\"content\": \"## WAHA Webhook\\nRemember to Configure your WAHA session **webhooks** to send proper event to **Webhook URL** from **WAHA Trigger** node\", \"height\": 629.9407047373624, \"width\": 327.12752630406015, \"color\": 6}, \"name\": \"Sticky Note1\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [1320, 740]}, {\"parameters\": {\"resource\": \"Chatting\", \"operation\": \"Send Seen\", \"participant\": \"\", \"requestOptions\": {}}, \"name\": \"Send Seen\", \"type\": \"@devlikeapro/n8n-nodes-waha.WAHA\", \"typeVersion\": 202409, \"position\": [1880, 760]}, {\"parameters\": {\"content\": \"## Always Send Seen\", \"height\": 256.1783237728081, \"width\": 568.1572184210546, \"color\": 4}, \"name\": \"Sticky Note6\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [1840, 680]}, {\"parameters\": {\"rules\": {\"values\": [{\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"leftValue\": \"true\", \"rightValue\": \"true\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\"}}], \"combinator\": \"and\"}}, {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"id\": \"242f1ea8-0d3f-491c-a60b-1ac78c82488f\", \"leftValue\": \"true\", \"rightValue\": \"true\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\", \"name\": \"filter.operator.equals\"}}], \"combinator\": \"and\"}}]}, \"options\": {\"allMatchingOutputs\": true}}, \"name\": \"Switch\", \"type\": \"n8n-nodes-base.switch\", \"typeVersion\": 3.1, \"position\": [1680, 940]}], \"connections\": {\"body=video\": {\"main\": [[{\"node\": \"HTTP Request - WAHA Send Video\", \"type\": \"main\", \"index\": 0}]]}, \"WAHA Trigger\": {\"main\": [[], [{\"node\": \"Switch\", \"type\": \"main\", \"index\": 0}]]}, \"Switch\": {\"main\": [[{\"node\": \"Send Seen\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"body=video\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"WAHA\", \"nodes\": [{\"parameters\": {\"events\": \"message\", \"webhookId\": \"1234567890\"}, \"name\": \"WA<PERSON> Trigger\", \"type\": \"wahaTrigger\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"url\": \"https://api.waha.com/v1/messages/{{ $node[\\\"WAHA Trigger\\\"].data[\\\"message_id\\\"] }}/seen\", \"method\": \"POST\", \"headers\": {\"Content-Type\": \"application/json\", \"Authorization\": \"Bearer <your_api_key>\"}, \"body\": {}}, \"name\": \"Mark as seen\", \"type\": \"httpRequest\", \"typeVersion\": 1, \"position\": [200, 0]}, {\"parameters\": {\"url\": \"https://api.waha.com/v1/messages\", \"method\": \"POST\", \"headers\": {\"Content-Type\": \"application/json\", \"Authorization\": \"Bearer <your_api_key>\"}, \"body\": \"{\\n  \\\"type\\\": \\\"video\\\",\\n  \\\"video\\\": {\\n    \\\"url\\\": \\\"https://example.com/video.mp4\\\"\\n  }\\n}\"}, \"name\": \"Send video\", \"type\": \"httpRequest\", \"typeVersion\": 1, \"position\": [600, 100]}, {\"parameters\": {\"conditions\": {\"message.body\": \"video\"}}, \"name\": \"Is video\", \"type\": \"if\", \"typeVersion\": 1, \"position\": [400, 0]}], \"connections\": {\"WAHA Trigger\": {\"main\": [[{\"node\": \"Mark as seen\", \"type\": \"main\", \"index\": 0}]]}, \"Mark as seen\": {\"main\": [[{\"node\": \"Is video\", \"type\": \"main\", \"index\": 0}]]}, \"Is video\": {\"main\": [[{\"node\": \"Send video\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"[Evolution / Bot Conversa] - Disparo em Massa -19/08 - \\u00e1s 18hs\", \"nodes\": [{\"parameters\": {\"operation\": \"limit\", \"keep\": \"lastItems\"}, \"name\": \"Item Lists\", \"type\": \"n8n-nodes-base.itemLists\", \"typeVersion\": 3, \"position\": [-880, 1760]}, {\"parameters\": {\"method\": \"POST\", \"url\": \"=https://evolutionapi.automagicbots.com.br/message/sendText/MSAHenrySantos\", \"sendHeaders\": true, \"headerParameters\": {\"parameters\": [{\"name\": \"apikey\", \"value\": \"1gozy92fyg3hlty3wkv7f\"}]}, \"sendBody\": true, \"bodyParameters\": {\"parameters\": [{\"name\": \"number\", \"value\": \"=5562981196678\"}, {\"name\": \"textMessage.text\", \"value\": \"=Disparo em massa do <PERSON> finalizado.\"}, {\"name\": \"options.delay\", \"value\": \"={{ 1200 }}\"}, {\"name\": \"options.presence\", \"value\": \"composing\"}, {\"name\": \"options.linkPreview\", \"value\": \"={{ true }}\"}, {\"name\": \"=options.mentions.everyOne\", \"value\": \"={{ true }}\"}]}, \"options\": {\"response\": {\"response\": {\"fullResponse\": true, \"neverError\": true}}}}, \"name\": \"EnviaMensagemTexto\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4, \"position\": [-680, 1760]}, {\"parameters\": {\"operation\": \"select\", \"table\": {\"__rl\": true, \"value\": \"leads\", \"mode\": \"list\", \"cachedResultName\": \"leads\"}, \"where\": {\"values\": [{\"column\": \"email\", \"value\": \"={{ $node[\\\"separa_em_lotes\\\"].json[\\\"EMAIL\\\"]  || '-----------------------'}}\"}, {\"column\": \"email2\", \"value\": \"={{ $node[\\\"separa_em_lotes\\\"].json[\\\"EMAIL\\\"]  || '-----------------------'}}\"}]}, \"combineConditions\": \"OR\", \"options\": {\"detailedOutput\": false}}, \"name\": \"pesquisa_lead\", \"type\": \"n8n-nodes-base.mySql\", \"typeVersion\": 2.1, \"position\": [-740, 2320]}, {\"parameters\": {\"documentId\": {\"__rl\": true, \"value\": \"1RUcWXDP4U5D2vFxVtbsJ_Et5Aj1VHxqnPsfyBBV-6pw\", \"mode\": \"id\"}, \"sheetName\": {\"__rl\": true, \"value\": 902416578, \"mode\": \"list\", \"cachedResultName\": \"P\\u00e1gina1\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1RUcWXDP4U5D2vFxVtbsJ_Et5Aj1VHxqnPsfyBBV-6pw/edit#gid=902416578\"}, \"options\": {}}, \"name\": \"le_planilha\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 3, \"position\": [-1580, 1960]}, {\"parameters\": {\"options\": {}}, \"name\": \"separa_em_lotes\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [-1400, 1960]}, {\"parameters\": {\"operation\": \"limit\", \"keep\": \"lastItems\"}, \"name\": \"pesquisa_lead1\", \"type\": \"n8n-nodes-base.itemLists\", \"typeVersion\": 3, \"position\": [-520, 2320]}, {\"parameters\": {}, \"name\": \"Fim\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [-1160, 1760]}, {\"parameters\": {\"method\": \"POST\", \"url\": \"=https://backend.botconversa.com.br/api/v1/webhook/subscriber/{{ $node[\\\"pesquisa_lead1\\\"].json[\\\"id_botconversa\\\"] }}/send_flow/\", \"sendHeaders\": true, \"headerParameters\": {\"parameters\": [{\"name\": \"API-KEY\", \"value\": \"adfd6c51-1101-48ba-bd0a-2f15568e2cfe\"}]}, \"sendBody\": true, \"bodyParameters\": {\"parameters\": [{\"name\": \"flow\", \"value\": \"4996848\"}]}, \"options\": {}}, \"name\": \"envia_fluxo\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 3, \"position\": [220, 2460]}, {\"parameters\": {\"operation\": \"select\", \"table\": {\"__rl\": true, \"value\": \"leads\", \"mode\": \"list\", \"cachedResultName\": \"leads\"}, \"where\": {\"values\": [{\"column\": \"email\", \"value\": \"={{ $node[\\\"separa_em_lotes\\\"].json[\\\"EMAIL\\\"]  || '-----------------------'}}\"}, {\"column\": \"email2\", \"value\": \"={{ $node[\\\"separa_em_lotes\\\"].json[\\\"EMAIL\\\"]  || '-----------------------'}}\"}]}, \"combineConditions\": \"OR\", \"options\": {\"detailedOutput\": false}}, \"name\": \"pesquisa_lead2\", \"type\": \"n8n-nodes-base.mySql\", \"typeVersion\": 2.1, \"position\": [-700, 1480]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"id\": \"9530f36b-c8ec-4ee4-971d-3805b7796088\", \"leftValue\": \"={{ $json[\\\"telefone\\\"] }}\", \"rightValue\": \"\", \"operator\": {\"type\": \"string\", \"operation\": \"notEmpty\", \"singleValue\": true}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"telefone_esta_preenchido\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2, \"position\": [-80, 1480]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"id\": \"6e0dc768-c500-4f44-afeb-462ab225bf6a\", \"leftValue\": \"={{ $now.toFormat('dd/MM/yyyy') }}\", \"rightValue\": \"19/08/2024\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\", \"name\": \"filter.operator.equals\"}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"verifica_data\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2, \"position\": [-1800, 1980]}, {\"parameters\": {\"conditions\": {\"boolean\": [{\"value1\": \"={{ $node[\\\"pesquisa_lead1\\\"].json[\\\"success\\\"] }}\", \"value2\": true}]}}, \"name\": \"nao_existe5\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 1, \"position\": [-320, 2320]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"id\": \"0523e64c-cc8d-4548-a121-87fe7b94e793\", \"leftValue\": \"={{ $node[\\\"separa_em_lotes\\\"].json[\\\"DISPARO\\\"] }}\", \"rightValue\": \"EVOLUTION\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\", \"name\": \"filter.operator.equals\"}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"disparo_via_evolution\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2, \"position\": [-1140, 1940]}, {\"parameters\": {}, \"name\": \"Evolution\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [-880, 1480]}, {\"parameters\": {}, \"name\": \"Bot Conversa\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [-920, 2320]}, {\"parameters\": {}, \"name\": \"N\\u00e3o faz nada\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [1160, 1960]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"id\": \"673f6d37-6e1b-4253-811e-4a6772f743a5\", \"leftValue\": \"={{ $node[\\\"nao_existe5\\\"].json[\\\"id_botconversa\\\"] }}\", \"rightValue\": \"\", \"operator\": {\"type\": \"number\", \"operation\": \"exists\", \"singleValue\": true}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"existe_id_bc\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2, \"position\": [-120, 2320]}, {\"parameters\": {\"operation\": \"limit\", \"keep\": \"lastItems\"}, \"name\": \"pesquisa_lead3\", \"type\": \"n8n-nodes-base.itemLists\", \"typeVersion\": 3, \"position\": [-480, 1480]}, {\"parameters\": {\"conditions\": {\"boolean\": [{\"value1\": \"={{ $node[\\\"pesquisa_lead3\\\"].json[\\\"success\\\"] }}\", \"value2\": true}]}}, \"name\": \"nao_existe\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 1, \"position\": [-300, 1480]}, {\"parameters\": {\"jsCode\": \"return {aleatorio: Math.floor(Math.random() * 4)};\"}, \"name\": \"Retorna Output Aleatorio2\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 1, \"position\": [400, 2460]}, {\"parameters\": {\"value1\": \"={{ $json[\\\"aleatorio\\\"] }}\", \"rules\": {\"rules\": [{\"operation\": \"equal\"}, {\"operation\": \"equal\", \"value2\": 1, \"output\": 1}, {\"operation\": \"equal\", \"value2\": 2, \"output\": 2}]}, \"fallbackOutput\": 3}, \"name\": \"Saida Aleatoria2\", \"type\": \"n8n-nodes-base.switch\", \"typeVersion\": 1, \"position\": [560, 2460]}, {\"parameters\": {\"amount\": 40, \"unit\": \"seconds\"}, \"name\": \"40s2\", \"type\": \"n8n-nodes-base.wait\", \"typeVersion\": 1, \"position\": [880, 2440]}, {\"parameters\": {\"amount\": 45, \"unit\": \"seconds\"}, \"name\": \"45s2\", \"type\": \"n8n-nodes-base.wait\", \"typeVersion\": 1, \"position\": [880, 2580]}, {\"parameters\": {\"amount\": 50, \"unit\": \"seconds\"}, \"name\": \"50s2\", \"type\": \"n8n-nodes-base.wait\", \"typeVersion\": 1, \"position\": [880, 2720]}, {\"parameters\": {\"amount\": 53, \"unit\": \"seconds\"}, \"name\": \"53s2\", \"type\": \"n8n-nodes-base.wait\", \"typeVersion\": 1, \"position\": [880, 2860]}, {\"parameters\": {\"method\": \"POST\", \"url\": \"=https://evolutionapi.automagicbots.com.br/message/sendText/MSAHenrySantos\", \"sendHeaders\": true, \"headerParameters\": {\"parameters\": [{\"name\": \"apikey\", \"value\": \"1gozy92fyg3hlty3wkv7f\"}]}, \"sendBody\": true, \"bodyParameters\": {\"parameters\": [{\"name\": \"number\", \"value\": \"={{ $node[\\\"pesquisa_lead3\\\"].json[\\\"telefone\\\"] }}\"}, {\"name\": \"textMessage.text\", \"value\": \"=\\ud83d\\udea8 Convite Exclusivo para Donos de Sal\\u00e3o de Beleza \\ud83d\\udea8\\n\\nFala! Aqui \\u00e9 a Geriane da equipe Henry Santos. \\ud83d\\udc4b\\n\\nTenho uma not\\u00edcia urgente para voc\\u00ea! No dia 27/08, \\u00e0s 19h, O Henry voai realizar uma Sala Secreta no Zoom, onde vou revelar os detalhes da maior Imers\\u00e3o para Sal\\u00f5es Autoadministr\\u00e1veis do Brasil. \\ud83d\\udca1\\n\\nEssa \\u00e9 sua chance de transformar seu sal\\u00e3o em um neg\\u00f3cio que cresce e lucra, mesmo sem voc\\u00ea estar presente. Quer saber mais?\\n\\n\\ud83c\\udfaf Garanta sua vaga agora: https://salaolucrativo.gestaodesaloes.com.br/pre-venda-imersa-sa\\n\\nNos vemos l\\u00e1!\"}, {\"name\": \"options.delay\", \"value\": \"={{ 1200 }}\"}, {\"name\": \"options.presence\", \"value\": \"composing\"}, {\"name\": \"options.linkPreview\", \"value\": \"={{ false }}\"}, {\"name\": \"=options.mentions.everyOne\", \"value\": \"={{ true }}\"}]}, \"options\": {\"response\": {\"response\": {\"fullResponse\": true, \"neverError\": true}}}}, \"name\": \"EnviaMensagemTexto6\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4, \"position\": [260, 1300]}, {\"parameters\": {\"rule\": {\"interval\": [{\"field\": \"weeks\", \"triggerAtDay\": [1], \"triggerAtHour\": 18}]}}, \"name\": \"19/08 - 18hrs\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.1, \"position\": [-1960, 1980]}], \"connections\": {\"Item Lists\": {\"main\": [[{\"node\": \"EnviaMensagemTexto\", \"type\": \"main\", \"index\": 0}]]}, \"pesquisa_lead\": {\"main\": [[{\"node\": \"pesquisa_lead1\", \"type\": \"main\", \"index\": 0}]]}, \"le_planilha\": {\"main\": [[{\"node\": \"separa_em_lotes\", \"type\": \"main\", \"index\": 0}]]}, \"separa_em_lotes\": {\"main\": [[{\"node\": \"Fim\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"disparo_via_evolution\", \"type\": \"main\", \"index\": 0}]]}, \"pesquisa_lead1\": {\"main\": [[{\"node\": \"nao_existe5\", \"type\": \"main\", \"index\": 0}]]}, \"envia_fluxo\": {\"main\": [[{\"node\": \"Retorna Output Aleatorio2\", \"type\": \"main\", \"index\": 0}]]}, \"pesquisa_lead2\": {\"main\": [[{\"node\": \"pesquisa_lead3\", \"type\": \"main\", \"index\": 0}]]}, \"telefone_esta_preenchido\": {\"main\": [[{\"node\": \"EnviaMensagemTexto6\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"separa_em_lotes\", \"type\": \"main\", \"index\": 0}]]}, \"verifica_data\": {\"main\": [[{\"node\": \"le_planilha\", \"type\": \"main\", \"index\": 0}]]}, \"nao_existe5\": {\"main\": [[{\"node\": \"separa_em_lotes\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"existe_id_bc\", \"type\": \"main\", \"index\": 0}]]}, \"disparo_via_evolution\": {\"main\": [[{\"node\": \"Evolution\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Bot Conversa\", \"type\": \"main\", \"index\": 0}]]}, \"Evolution\": {\"main\": [[{\"node\": \"pesquisa_lead2\", \"type\": \"main\", \"index\": 0}]]}, \"Bot Conversa\": {\"main\": [[{\"node\": \"pesquisa_lead\", \"type\": \"main\", \"index\": 0}]]}, \"N\\u00e3o faz nada\": {\"main\": [[{\"node\": \"separa_em_lotes\", \"type\": \"main\", \"index\": 0}]]}, \"existe_id_bc\": {\"main\": [[{\"node\": \"envia_fluxo\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"separa_em_lotes\", \"type\": \"main\", \"index\": 0}]]}, \"pesquisa_lead3\": {\"main\": [[{\"node\": \"nao_existe\", \"type\": \"main\", \"index\": 0}]]}, \"nao_existe\": {\"main\": [[{\"node\": \"separa_em_lotes\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"telefone_esta_preenchido\", \"type\": \"main\", \"index\": 0}]]}, \"Retorna Output Aleatorio2\": {\"main\": [[{\"node\": \"Saida Aleatoria2\", \"type\": \"main\", \"index\": 0}]]}, \"Saida Aleatoria2\": {\"main\": [[{\"node\": \"40s2\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"45s2\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"50s2\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"53s2\", \"type\": \"main\", \"index\": 0}]]}, \"40s2\": {\"main\": [[{\"node\": \"N\\u00e3o faz nada\", \"type\": \"main\", \"index\": 0}]]}, \"45s2\": {\"main\": [[{\"node\": \"N\\u00e3o faz nada\", \"type\": \"main\", \"index\": 0}]]}, \"50s2\": {\"main\": [[{\"node\": \"N\\u00e3o faz nada\", \"type\": \"main\", \"index\": 0}]]}, \"53s2\": {\"main\": [[{\"node\": \"N\\u00e3o faz nada\", \"type\": \"main\", \"index\": 0}]]}, \"EnviaMensagemTexto6\": {\"main\": [[{\"node\": \"Retorna Output Aleatorio2\", \"type\": \"main\", \"index\": 0}]]}, \"19/08 - 18hrs\": {\"main\": [[{\"node\": \"verifica_data\", \"type\": \"main\", \"index\": 0}]]}, \"Fim\": {\"main\": [[{\"node\": \"Item Lists\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {}, \"name\": \"When clicking \\u2018Test workflow\\u2019\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [-340, -40]}, {\"parameters\": {\"url\": \"https://api.elevenlabs.io/v1/speech-to-text\", \"method\": \"POST\", \"options\": {}, \"sendBody\": true, \"contentType\": \"multipart-form-data\", \"sendHeaders\": true, \"authentication\": \"genericCredentialType\", \"bodyParameters\": {\"parameters\": [{\"name\": \"file\", \"parameterType\": \"formBinaryData\", \"inputDataFieldName\": \"data\"}, {\"name\": \"model_id\", \"value\": \"scribe_v1\"}]}, \"genericAuthType\": \"httpCustomAuth\", \"headerParameters\": {\"parameters\": [{\"name\": \"Content-Type\", \"value\": \"multipart/form-data\"}]}}, \"name\": \"Create Transcript1\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [100, -40]}, {\"parameters\": {\"options\": {}, \"fileSelector\": \"/files/tmp/tst1.mp4\"}, \"name\": \"Read/Write Files from Disk\", \"type\": \"n8n-nodes-base.readWriteFile\", \"typeVersion\": 1, \"position\": [-120, -40]}], \"connections\": {\"Read/Write Files from Disk\": {\"main\": [[{\"node\": \"Create Transcript1\", \"type\": \"main\", \"index\": 0}]]}, \"When clicking \\u2018Test workflow\\u2019\": {\"main\": [[{\"node\": \"Read/Write Files from Disk\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Speech to text\", \"nodes\": [{\"parameters\": {}, \"name\": \"manualTrigger\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [250, 300]}, {\"parameters\": {\"filePath\": \"/files/tmp/tst1.mp4\"}, \"name\": \"readBinaryFile\", \"type\": \"n8n-nodes-base.readBinaryFile\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"url\": \"https://api.elevenlabs.io/v1/speech-to-text\", \"method\": \"POST\", \"multipart\": true, \"formBody\": {\"model_id\": \"scribe_v1\"}}, \"name\": \"httpRequest\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 1, \"position\": [650, 300]}], \"connections\": {\"manualTrigger\": {\"main\": [[{\"node\": \"readBinaryFile\", \"type\": \"main\", \"index\": 0}]]}, \"readBinaryFile\": {\"main\": [[{\"node\": \"httpRequest\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {}, \"name\": \"On clicking 'execute'\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [0, 300]}, {\"parameters\": {\"functionCode\": \"return [\\n  {\\n    json: {\\n      id: 0,\\n    }\\n  },\\n  {\\n    json: {\\n      id: 1,\\n    }\\n  },\\n  {\\n    json: {\\n      id: 2,\\n    }\\n  }\\n];\\n\"}, \"name\": \"Function\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [200, 300]}, {\"parameters\": {\"values\": {\"string\": [{\"name\": \"name\", \"value\": \"n8n\"}]}, \"options\": {}}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [600, 90]}, {\"parameters\": {\"values\": {\"string\": [{\"name\": \"name\", \"value\": \"nodemation\"}]}, \"options\": {}}, \"name\": \"Set1\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [600, 230]}, {\"parameters\": {\"rules\": {\"rules\": [{\"operation\": \"equal\"}, {\"output\": 1, \"value2\": 1, \"operation\": \"equal\"}, {\"output\": 2, \"value2\": 2, \"operation\": \"equal\"}]}, \"value1\": \"={{$node[\\\"Function\\\"].json[\\\"id\\\"]}}\", \"fallbackOutput\": 3}, \"name\": \"Switch\", \"type\": \"n8n-nodes-base.switch\", \"typeVersion\": 1, \"position\": [400, 300]}, {\"parameters\": {\"values\": {\"string\": [{\"name\": \"name\", \"value\": \"nathan\"}]}, \"options\": {}}, \"name\": \"Set2\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [600, 370]}, {\"parameters\": {}, \"name\": \"NoOp\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [600, 510]}], \"connections\": {\"Switch\": {\"main\": [[{\"node\": \"Set\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Set1\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Set2\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"NoOp\", \"type\": \"main\", \"index\": 0}]]}, \"Function\": {\"main\": [[{\"node\": \"Switch\", \"type\": \"main\", \"index\": 0}]]}, \"On clicking 'execute'\": {\"main\": [[{\"node\": \"Function\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {}, \"name\": \"manualTrigger\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [250, 300]}, {\"parameters\": {\"functionCode\": \"return [{id:0}, {id:1}, {id:2}]\"}, \"name\": \"Function\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"value\": \"={{$node[\\\"Function\\\"].data[\\\"id\\\"]}}\", \"cases\": [{\"value\": 0, \"output\": \"n8n\"}, {\"value\": 1, \"output\": \"nodemation\"}, {\"value\": 2, \"output\": \"nathan\"}]}, \"name\": \"Switch\", \"type\": \"n8n-nodes-base.switch\", \"typeVersion\": 1, \"position\": [650, 300]}, {\"parameters\": {\"name\": \"n8n\"}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [850, 200]}, {\"parameters\": {\"name\": \"nodemation\"}, \"name\": \"Set1\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [850, 300]}, {\"parameters\": {\"name\": \"nathan\"}, \"name\": \"Set2\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [850, 400]}, {\"parameters\": {}, \"name\": \"NoOp\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [850, 500]}], \"connections\": {\"manualTrigger\": {\"main\": [[{\"node\": \"Function\", \"type\": \"main\", \"index\": 0}]]}, \"Function\": {\"main\": [[{\"node\": \"Switch\", \"type\": \"main\", \"index\": 0}]]}, \"Switch\": {\"main\": [[{\"node\": \"Set\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Set1\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Set2\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"NoOp\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"id\": \"={{ $('Entered View  \\\"First Task - Create Task\\\"').item.json[\\\"id\\\"] }}\", \"base\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"={{ $item(\\\"0\\\").$node[\\\"Airtable Base ID's\\\"].json[\\\"base_id\\\"] }}\"}, \"table\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"={{ $item(\\\"0\\\").$node[\\\"Airtable Base ID's\\\"].json[\\\"table_automate_id\\\"] }}\"}, \"options\": {}, \"operation\": \"get\"}, \"name\": \"Get Automated Task\", \"type\": \"n8n-nodes-base.airtable\", \"typeVersion\": 2, \"position\": [1040, 660]}, {\"parameters\": {\"url\": \"=https://api.airtable.com/v0/{{ $item(\\\"0\\\").$node[\\\"Airtable Base ID's\\\"].json[\\\"base_id\\\"] }}/{{ $item(\\\"0\\\").$node[\\\"Airtable Base ID's\\\"].json[\\\"table_task_id\\\"] }}\", \"method\": \"POST\", \"options\": {}, \"jsonBody\": \"={\\n    \\\"records\\\": [\\n        {\\n            \\\"fields\\\": {\\n                \\\"Status\\\": \\\"Todo\\\",\\n                \\\"Task Name\\\": \\\"{{ $item(\\\"0\\\").$node[\\\"Get Task Template\\\"].json[\\\"Template Name\\\"] }}\\\",\\n                \\\"Task Description\\\": \\\"{{ $('Get Task Template').item.json[\\\"Description\\\"].replace(/\\\\r?\\\\n/g, \\\"\\\\\\\\n\\\") }}\\\",\\n                \\\"Kickoff Date\\\": \\\"{{ $('Calculate Dates').item.json[\\\"Kickoff Date\\\"] }}\\\",\\n                \\\"Soft Due Date\\\": \\\"{{ $('Calculate Dates').item.json[\\\"Soft Due Date\\\"] }}\\\",\\n                \\\"Hard Due Date\\\": \\\"{{ $('Calculate Dates').item.json[\\\"Hard Due Date\\\"] }}\\\",\\n                \\\"Assignee\\\": [\\n                    \\\"{{ $('Get Assignee').item.json[\\\"id\\\"] }}\\\"\\n                ],\\n                \\\"Template\\\": [\\n                    \\\"{{ $('Get Task Template').item.json[\\\"id\\\"] }}\\\"\\n                ],\\n                \\\"Client\\\": [\\n                    \\\"{{ $('Get Client').item.json[\\\"id\\\"] }}\\\"\\n                ]\\n            }\\n        }\\n    ]\\n}\", \"sendBody\": true, \"sendHeaders\": true, \"specifyBody\": \"json\", \"authentication\": \"predefinedCredentialType\", \"headerParameters\": {\"parameters\": [{\"name\": \"Content-Type\", \"value\": \"application/json\"}]}, \"nodeCredentialType\": \"airtableTokenApi\"}, \"name\": \"Create Task\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [2140, 660]}, {\"parameters\": {\"id\": \"={{ $item(\\\"0\\\").$node[\\\"Get Automated Task\\\"].json[\\\"Template\\\"][\\\"0\\\"] }}\", \"base\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"={{ $item(\\\"0\\\").$node[\\\"Airtable Base ID's\\\"].json[\\\"base_id\\\"] }}\"}, \"table\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"={{ $item(\\\"0\\\").$node[\\\"Airtable Base ID's\\\"].json[\\\"table_template_id\\\"] }}\"}, \"options\": {}, \"operation\": \"get\"}, \"name\": \"Get Task Template\", \"type\": \"n8n-nodes-base.airtable\", \"typeVersion\": 2, \"position\": [1240, 660]}, {\"parameters\": {\"id\": \"={{ $item(\\\"0\\\").$node[\\\"Get Automated Task\\\"].json[\\\"Assigned Team Member\\\"][\\\"0\\\"] }}\", \"base\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"={{ $item(\\\"0\\\").$node[\\\"Airtable Base ID's\\\"].json[\\\"base_id\\\"] }}\"}, \"table\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"={{ $item(\\\"0\\\").$node[\\\"Airtable Base ID's\\\"].json[\\\"table_team_id\\\"] }}\"}, \"options\": {}, \"operation\": \"get\"}, \"name\": \"Get Assignee\", \"type\": \"n8n-nodes-base.airtable\", \"typeVersion\": 2, \"position\": [1460, 660]}, {\"parameters\": {\"id\": \"={{ $item(\\\"0\\\").$node[\\\"Get Automated Task\\\"].json[\\\"Client\\\"][\\\"0\\\"] }}\", \"base\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"={{ $item(\\\"0\\\").$node[\\\"Airtable Base ID's\\\"].json[\\\"base_id\\\"] }}\"}, \"table\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"={{ $item(\\\"0\\\").$node[\\\"Airtable Base ID's\\\"].json[\\\"table_clients_id\\\"] }}\"}, \"options\": {}, \"operation\": \"get\"}, \"name\": \"Get Client\", \"type\": \"n8n-nodes-base.airtable\", \"typeVersion\": 2, \"position\": [1660, 660]}, {\"parameters\": {\"jsCode\": \"// Retrieve values from the previous node\\nconst firstTaskCreated = $item(\\\"0\\\").$node[\\\"Get Automated Task\\\"].json[\\\"First Task Created\\\"];\\nconst startDate = $item(\\\"0\\\").$node[\\\"Get Automated Task\\\"].json[\\\"Start Date\\\"];\\nconst lastTaskCreated = $item(\\\"0\\\").$node[\\\"Get Automated Task\\\"].json[\\\"Last Task Created\\\"];\\nconst timeValue = $item(\\\"0\\\").$node[\\\"Get Automated Task\\\"].json[\\\"Time Value\\\"];\\nconst daysForSoftDueDate = $item(\\\"0\\\").$node[\\\"Get Automated Task\\\"].json[\\\"Days for Soft Due Date\\\"];\\n\\n// Helper function to add days to a date\\nfunction addDays(date, days) {\\n  let result = new Date(date);\\n  result.setDate(result.getDate() + days);\\n  return result;\\n}\\n\\n// Helper function to format date in MM/DD/YYYY\\nfunction formatDate(date) {\\n  return (date.getMonth() + 1) + '/' + date.getDate() + '/' + date.getFullYear();\\n}\\n\\n// Calculate Kickoff Date\\nlet kickoffDate;\\nif (firstTaskCreated === \\\"false\\\") {\\n  kickoffDate = new Date(startDate);\\n} else {\\n  kickoffDate = addDays(new Date(lastTaskCreated), timeValue);\\n}\\n\\n// Calculate Soft Due Date\\nconst softDueDate = addDays(kickoffDate, timeValue - daysForSoftDueDate);\\n\\n// Calculate Hard Due Date\\nconst hardDueDate = addDays(kickoffDate, timeValue);\\n\\n// Get today's date\\nconst today = new Date();\\n\\n// Calculate Next Task Creation Date (Hard Due Date minus 1 day)\\nconst nextTaskCreationDate = addDays(hardDueDate, -1);\\n\\n// Prepare the output\\nreturn [{\\n  json: {\\n    \\\"Kickoff Date\\\": formatDate(kickoffDate),\\n    \\\"Soft Due Date\\\": formatDate(softDueDate),\\n    \\\"Hard Due Date\\\": formatDate(hardDueDate),\\n    \\\"Today\\\": formatDate(today),\\n    \\\"Next Task Creation Date\\\": formatDate(nextTaskCreationDate)\\n  }\\n}];\\n\"}, \"name\": \"Calculate Dates\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 2, \"position\": [1880, 660]}, {\"parameters\": {\"url\": \"=https://api.airtable.com/v0/{{ $item(\\\"0\\\").$node[\\\"Airtable Base ID's\\\"].json[\\\"base_id\\\"] }}/{{ $item(\\\"0\\\").$node[\\\"Airtable Base ID's\\\"].json[\\\"table_automate_id\\\"] }}\", \"method\": \"PATCH\", \"options\": {}, \"jsonBody\": \"={\\n    \\\"records\\\": [\\n        {\\n            \\\"id\\\": \\\"{{ $item(\\\"0\\\").$node[\\\"Get Automated Task\\\"].json[\\\"id\\\"] }}\\\",\\n            \\\"fields\\\": {\\n                \\\"First Task Created\\\": \\\"true\\\",\\n                \\\"Last Task Created\\\": \\\"{{ $('Calculate Dates').item.json[\\\"Today\\\"] }}\\\",\\n                \\\"Next Task Creation Date\\\": \\\"{{ $('Calculate Dates').item.json[\\\"Next Task Creation Date\\\"] }}\\\"\\n            }\\n        }\\n    ]\\n}\", \"sendBody\": true, \"sendHeaders\": true, \"specifyBody\": \"json\", \"authentication\": \"predefinedCredentialType\", \"headerParameters\": {\"parameters\": [{\"name\": \"Content-Type\", \"value\": \"application/json\"}]}, \"nodeCredentialType\": \"airtableTokenApi\"}, \"name\": \"Update Automated Record\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [2420, 660]}, {\"parameters\": {\"select\": \"channel\", \"channelId\": {\"__rl\": true, \"mode\": \"list\", \"value\": \"\"}, \"otherOptions\": {}}, \"name\": \"Notify Assignee\", \"type\": \"n8n-nodes-base.slack\", \"typeVersion\": 2.1, \"position\": [2680, 660]}, {\"parameters\": {\"width\": 577.8258549588782, \"height\": 149.31896574204097, \"content\": \"## Resources\\nThe Airtable template can be found here - https://www.airtable.com/universe/expDZ9rbZ9ZwZuTmX/recurring-tasks-automation\"}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [380, 240]}, {\"parameters\": {\"width\": 519.2937872252622, \"height\": 478.35585536865557, \"content\": \"### These nodes should be adapted to your custom Airtable Base. These nodes and the field names correspond to the template fields, but will not work if your tables field names, field type are different\"}, \"name\": \"Sticky Note1\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [1860, 520]}, {\"parameters\": {\"baseId\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"appPL3AkBc0iw5Z3x\"}, \"tableId\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"tblp4KpAUGY9RqbMj\"}, \"pollTimes\": {\"item\": [{\"mode\": \"everyMinute\"}]}, \"triggerField\": \"updated_at\", \"authentication\": \"airtableTokenApi\", \"additionalFields\": {\"viewId\": \"viwsays8X5yn5Xl7g\"}}, \"name\": \"Entered View  \\\"First Task - Create Task\\\"\", \"type\": \"n8n-nodes-base.airtableTrigger\", \"typeVersion\": 1, \"position\": [500, 660]}, {\"parameters\": {\"width\": 408.1448240473296, \"height\": 146.75862096834132, \"content\": \"## Walkthrough and Overview\\n\\n### https://www.youtube.com/watch?v=if3wr0tY-gk\"}, \"name\": \"Sticky Note2\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [960, 240]}, {\"parameters\": {\"width\": 400.220686283071, \"height\": 575.7793015940845, \"content\": \"## Setup Checklist\\n\\n1. Go to the Airtable Template and copy the latest version of the base\\n2. Go to the `Automate` table and open the view `First Task - Create Task`. From here, copy the BaseId, TableId and ViewId into the trigger. Make that the field \\\"updated_at\\\" is visible in the \\\"First Task - Create Task\\\" View\\n3. Input your Airtable Id's in the second node \\\"Airtable Base ID's\\\"\\n\\n### The setup is now complete, now for testing:\\n\\n1. Go to the Airtable Interface Page called \\\"Automate a Template \\u2699\\ufe0f\\\" and create an entry utilizing the dummy data.\\n2. **Important** If you want to test the automation live, the Start Date should be set to TODAY. Please ensure your n8n automation is live.\"}, \"name\": \"Sticky Note3\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [0, 520]}, {\"parameters\": {\"fields\": {\"values\": [{\"name\": \"base_id\", \"stringValue\": \"appVtUCDmP7LnG8bV\"}, {\"name\": \"table_task_id\", \"stringValue\": \"tblbkEKwqEAuY6kBW\"}, {\"name\": \"table_template_id\", \"stringValue\": \"tbl7f8iV3qLUvirPX\"}, {\"name\": \"table_clients_id\", \"stringValue\": \"tbljzJBlyrHwzEXXK\"}, {\"name\": \"table_team_id\", \"stringValue\": \"tblKlBfYzCWVzY0Mh\"}, {\"name\": \"table_automate_id\", \"stringValue\": \"tblvMBrTFj5CI1kUH\"}]}, \"include\": \"none\", \"options\": {}}, \"name\": \"Airtable Base ID's\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [720, 660]}], \"connections\": {\"Get Client\": {\"main\": [[{\"node\": \"Calculate Dates\", \"type\": \"main\", \"index\": 0}]]}, \"Create Task\": {\"main\": [[{\"node\": \"Update Automated Record\", \"type\": \"main\", \"index\": 0}]]}, \"Get Assignee\": {\"main\": [[{\"node\": \"Get Client\", \"type\": \"main\", \"index\": 0}]]}, \"Calculate Dates\": {\"main\": [[{\"node\": \"Create Task\", \"type\": \"main\", \"index\": 0}]]}, \"Get Task Template\": {\"main\": [[{\"node\": \"Get Assignee\", \"type\": \"main\", \"index\": 0}]]}, \"Airtable Base ID's\": {\"main\": [[{\"node\": \"Get Automated Task\", \"type\": \"main\", \"index\": 0}]]}, \"Get Automated Task\": {\"main\": [[{\"node\": \"Get Task Template\", \"type\": \"main\", \"index\": 0}]]}, \"Update Automated Record\": {\"main\": [[{\"node\": \"Notify Assignee\", \"type\": \"main\", \"index\": 0}]]}, \"Entered View  \\\"First Task - Create Task\\\"\": {\"main\": [[{\"node\": \"Airtable Base ID's\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"Set Up Import - GET\", \"nodes\": [{\"parameters\": {\"path\": \"GetOnboardingHooks\", \"responseMode\": \"responseNode\", \"options\": {}}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 2, \"position\": [0, 0]}, {\"parameters\": {\"documentId\": {\"__rl\": true, \"value\": \"1t6UxAyhrQazJ19qRcBj57xyFYcloO6gVQ2sbx_SD9mI\", \"mode\": \"list\", \"cachedResultName\": \"Mega Database\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1t6UxAyhrQazJ19qRcBj57xyFYcloO6gVQ2sbx_SD9mI/edit?usp=drivesdk\"}, \"sheetName\": {\"__rl\": true, \"value\": 1422831539, \"mode\": \"list\", \"cachedResultName\": \"Onboarding\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1t6UxAyhrQazJ19qRcBj57xyFYcloO6gVQ2sbx_SD9mI/edit#gid=1422831539\"}, \"options\": {}}, \"name\": \"Google Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.5, \"position\": [220, 0]}, {\"parameters\": {\"respondWith\": \"json\", \"responseBody\": \"={\\n  \\\"ImportSettingsWebhookGet\\\": \\\"{{ $json.ImportSettingsWebhookGet }}\\\",\\n  \\\"ExportSettingsWebhookPost\\\": \\\"{{ $json.ExportSettingsWebhookPost }}\\\"\\n} \", \"options\": {}}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1.1, \"position\": [440, 0]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Google Sheets\", \"type\": \"main\", \"index\": 0}]]}, \"Google Sheets\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Get Onboarding Hooks\", \"nodes\": [{\"parameters\": {\"httpMethod\": \"GET\", \"path\": \"GetOnboardingHooks\"}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"sheetName\": \"Onboarding\", \"database\": \"Mega Database\"}, \"name\": \"Google Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 1, \"position\": [220, 0]}, {\"parameters\": {\"respondWith\": \"json\", \"responseBody\": \"{\\n  \\\"ImportSettingsWebhookGet\\\": \\\"https://n8n.io\\\",\\n  \\\"ExportSettingsWebhookPost\\\": \\\"https://n8n.io\\\"\\n}\"}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1, \"position\": [440, 0]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Google Sheets\", \"type\": \"main\", \"index\": 0}]]}, \"Google Sheets\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Error trigger\", \"nodes\": [{\"parameters\": {}, \"name\": \"<PERSON>rro<PERSON> Trigger\", \"type\": \"n8n-nodes-base.errorTrigger\", \"typeVersion\": 1, \"position\": [-240, 0]}, {\"parameters\": {\"chatId\": \"5346776407\", \"text\": \"=Error: {{ $json.execution.error.message }}\\n\\nUltimo nodo ejecutado: {{ $json.execution.lastNodeExecuted }}\", \"additionalFields\": {\"appendAttribution\": false}}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [-20, 0]}], \"connections\": {\"Error Trigger\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"Error trigger\", \"nodes\": [{\"parameters\": {\"chatId\": \"5346776407\", \"message\": \"=Error: {{ $json.error.message }}\\nLast Node: {{ $json.lastNode }}\"}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1, \"position\": [500, 300]}, {\"parameters\": {}, \"name\": \"Error Trigger\", \"type\": \"n8n-nodes-base.errorTrigger\", \"typeVersion\": 1, \"position\": [300, 300]}], \"connections\": {\"Error Trigger\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"AppTest Collect contacts via SalesQL\", \"nodes\": [{\"parameters\": {\"path\": \"onboarding/leadgeneration/tests/CollectContactsViaSalesQL\", \"responseMode\": \"responseNode\", \"options\": {}}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 2, \"position\": [0, 0]}, {\"parameters\": {\"respondWith\": \"text\", \"responseBody\": \"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n  <link type=\\\"image/png\\\" sizes=\\\"16x16\\\" rel=\\\"icon\\\" href=\\\"https://i.postimg.cc/gJf9MgWR/icons8-32.png\\\">\\n  <meta charset=\\\"UTF-8\\\">\\n  <title>Collect contacts via SalesQL</title>\\n  <link href=\\\"https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap\\\" rel=\\\"stylesheet\\\">\\n\\n  <style>\\n    :root {\\n      --deep-blue: #081E2F;\\n      --primary-yellow: #FCB305;\\n      --red: #D32F2F;\\n      --red-hover: #C62828;\\n      --green: #2E7D32;\\n      --green-hover: #1B5E20;\\n      --blue: #197502;\\n      --blue-dark: #1555C0;\\n      --blue-light: #4245F5;\\n    }\\n    body {\\n      font-family: 'Open Sans', sans-serif;\\n      background: #F8F9FA;\\n      padding: 2rem;\\n      max-width: 800px;\\n      margin: 0 auto;\\n      min-height: 100vh;\\n    }\\n    #titlePage {\\n      text-align: center;\\n      padding: 3rem 1rem;\\n    }\\n    #titlePage h1 {\\n      font-size: 32px;\\n      color: var(--deep-blue);\\n      margin-bottom: 1.5rem;\\n    }\\n    .quiz-info {\\n      background: white;\\n      padding: 2rem;\\n      border-radius: 12px;\\n      box-shadow: 0 4px 12px rgba(8, 30, 47, 0.1);\\n      margin: 2rem auto;\\n      max-width: 500px;\\n    }\\n    .info-item {\\n      font-size: 16px;\\n      color: #4A4A4A;\\n      margin: 1rem 0;\\n      display: flex;\\n      justify-content: space-between;\\n      align-items: center;\\n    }\\n    #timer {\\n      position: fixed;\\n      top: 20px;\\n      right: 20px;\\n      background: var(--green);\\n      color: white;\\n      padding: 8px 16px;\\n      border-radius: 20px;\\n      font-weight: 600;\\n      box-shadow: 0 2px 6px rgba(46, 125, 50, 0.2);\\n    }\\n    .hidden {\\n      display: none;\\n    }\\n    .question {\\n      background: white;\\n      border-radius: 8px;\\n      padding: 1.5rem;\\n      margin-bottom: 1.5rem;\\n      box-shadow: 0 2px 8px rgba(8, 30, 47, 0.1);\\n      transition: all 0.3s ease;\\n    }\\n    .question h3 {\\n      font-size: 16px;\\n      font-weight: 600;\\n      color: var(--deep-blue);\\n      margin: 0 0 1rem 0;\\n    }\\n    label {\\n      display: block;\\n      margin: 0.5rem 0;\\n      padding: 0.75rem;\\n      border-radius: 4px;\\n      font-size: 14px;\\n      color: #4A4A4A;\\n      cursor: pointer;\\n      transition: all 0.2s ease;\\n    }\\n    label:hover {\\n      background: rgba(25, 118, 210, 0.04);\\n    }\\n    /* Single-answer (radio) styled as circles */\\n    input[type=\\\"radio\\\"] {\\n      margin-right: 0.75rem;\\n      accent-color: var(--green);\\n      border-radius: 50%;\\n      width: 16px;\\n      height: 16px;\\n    }\\n    /* Multi-answer (checkbox) */\\n    input[type=\\\"checkbox\\\"] {\\n      margin-right: 0.75rem;\\n      accent-color: var(--green);\\n    }\\n    input[type=\\\"text\\\"],\\n    input[type=\\\"number\\\"] {\\n      padding: 0.5rem;\\n      border: 1px solid #DEE2E6;\\n      border-radius: 4px;\\n      font-size: 14px;\\n      width: 100%;\\n      box-sizing: border-box;\\n    }\\n    /* Make \\\"Profession\\\" field read-only */\\n    input[disabled] {\\n      background: #f3f3f3;\\n      color: #888;\\n    }\\n    button {\\n      background: var(--green);\\n      color: white;\\n      border: none;\\n      padding: 12px 24px;\\n      border-radius: 6px;\\n      font-weight: 700;\\n      font-size: 15px;\\n      cursor: pointer;\\n      transition: all 0.3s ease;\\n      display: block;\\n      margin: 2rem auto 0;\\n      letter-spacing: 0.5px;\\n    }\\n    button:hover {\\n      background: var(--green-hover);\\n      transform: translateY(-2px);\\n      box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);\\n    }\\n    #result {\\n      background: white;\\n      padding: 1.5rem;\\n      border-radius: 8px;\\n      text-align: center;\\n      margin-top: 2rem;\\n      box-shadow: 0 2px 8px rgba(8, 30, 47, 0.1);\\n    }\\n    .correct {\\n      border-left: 4px solid var(--green);\\n      background: #F0F9FF;\\n    }\\n    .incorrect {\\n      border-left: 4px solid var(--red);\\n    }\\n    .correct-answer {\\n      background: #E8F5E9 !important;\\n      border: 1px solid var(--green);\\n      border-radius: 4px;\\n      padding: 2px 4px;\\n      margin-top: 0.5rem;\\n      display: inline-block;\\n    }\\n    @media (max-width: 600px) {\\n      body {\\n        padding: 1rem;\\n      }\\n      .question {\\n        padding: 1rem;\\n      }\\n      button {\\n        width: 100%;\\n        padding: 14px 20px;\\n      }\\n      #timer {\\n        top: 10px;\\n        right: 10px;\\n        font-size: 14px;\\n      }\\n    }\\n  </style>\\n</head>\\n<body>\\n  <!-- Title Page -->\\n  <div id=\\\"titlePage\\\">\\n    <h1>Collect contacts via SalesQL</h1>\\n    <div class=\\\"quiz-info\\\">\\n      <div class=\\\"info-item\\\">\\n        <span>Questions:</span>\\n        <strong>15</strong>\\n      </div>\\n      <div class=\\\"info-item\\\">\\n        <span>Time Limit:</span>\\n        <strong>5 minutes</strong>\\n      </div>\\n      <div class=\\\"info-item\\\">\\n        <span>Passing Score:</span>\\n        <strong>80%</strong>\\n      </div>\\n      <button id=\\\"startButton\\\" onclick=\\\"startQuiz()\\\">Start Exam</button>\\n    </div>\\n  </div>\\n\\n  <!-- Quiz Container -->\\n  <div id=\\\"quizContainer\\\" class=\\\"hidden\\\">\\n    <div id=\\\"timer\\\">00:00</div>\\n    <h1>Collect contacts via SalesQL</h1>\\n\\n    <form id=\\\"examForm\\\">\\n      <!-- User Data (0 points) -->\\n      <div class=\\\"question\\\" data-points=\\\"0\\\">\\n        <h3>Enter Your Information</h3>\\n        <label>\\n          Your Name:\\n          <input type=\\\"text\\\" name=\\\"fullName\\\" placeholder=\\\"e.g., John Doe\\\">\\n        </label>\\n        <label>\\n          Your Profession:\\n          <input type=\\\"text\\\" name=\\\"profession\\\" value=\\\"Lead Generator\\\" disabled>\\n        </label>\\n        <label>\\n          Your Recruiter:\\n          <select name=\\\"recruiter\\\">\\n            <option value=\\\"Anastasia Fadeeva\\\">Anastasia Fadeeva</option>\\n            <option value=\\\"Elena Ermakova\\\">Elena Ermakova</option>\\n            <option value=\\\"Anna Aleksandrova\\\">Anna Aleksandrova</option>\\n            <option value=\\\"Sabina Gasanova\\\">Sabina Gasanova</option>\\n          </select>\\n        </label>\\n      </div>\\n\\n      <!-- Single-Answer Questions (10) -->\\n      <!-- Q1 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>1. Which platforms are compatible with SalesQL?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"BasicSNS\\\" data-correct=\\\"true\\\">\\n          LinkedIn (Basic), Sales Navigator, and Recruiter\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"FBAds\\\">\\n          Facebook Ads, Instagram DMs, and Twitter\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"TiktokOnly\\\">\\n          TikTok and Snapchat only\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"JustGmail\\\">\\n          Gmail and Outlook exclusively\\n        </label>\\n      </div>\\n\\n      <!-- Q2 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>2. What is the primary function of SalesQL for lead generation?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"SchedulesPosts\\\">\\n          Scheduling LinkedIn posts automatically\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"ExtractContact\\\" data-correct=\\\"true\\\">\\n          Extracting verified email addresses and phone numbers from LinkedIn\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"ProfileDesign\\\">\\n          Designing LinkedIn profile banners\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"AutoSendConnect\\\">\\n          Auto-sending LinkedIn connection requests in bulk\\n        </label>\\n      </div>\\n\\n      <!-- Q3 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>3. Which login method is recommended when setting up SalesQL?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q3\\\" value=\\\"CustomAuth\\\">\\n          A custom authentication token from your CRM\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q3\\\" value=\\\"GoogleSignIn\\\" data-correct=\\\"true\\\">\\n          Sign in with <NAME_EMAIL>\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q3\\\" value=\\\"OutlookCreds\\\">\\n          An Outlook.com address for convenience\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q3\\\" value=\\\"NoLoginReq\\\">\\n          No login is required, it works anonymously\\n        </label>\\n      </div>\\n\\n      <!-- Q4 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>4. What step should you do first if the SalesQL extension isn\\u2019t working properly on LinkedIn?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"UninstallOS\\\">\\n          Uninstall your entire operating system\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"RefreshRestart\\\" data-correct=\\\"true\\\">\\n          Refresh LinkedIn and restart Chrome to see if that resolves the issue\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"ShiftToEdge\\\">\\n          Switch to Microsoft Edge immediately\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"IgnoreIssue\\\">\\n          Ignore the problem\\u2014SalesQL is always glitchy\\n        </label>\\n      </div>\\n\\n      <!-- Q5 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>5. How does SalesQL handle extension updates (as of October 2023)?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"ManualDL\\\">\\n          Users must manually download updates each month\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"AutoUpd\\\" data-correct=\\\"true\\\">\\n          The extension updates automatically via the Chrome Web Store\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"NoUpdates\\\">\\n          There are no updates; it\\u2019s permanently on one version\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"BetaTester\\\">\\n          Only beta testers receive auto-updates\\n        </label>\\n      </div>\\n\\n      <!-- Q6 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>6. Which of the following is NOT a requirement for manually installing SalesQL?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q6\\\" value=\\\"DownloadZip\\\">\\n          Downloading and extracting the ZIP file\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q6\\\" value=\\\"EnableDev\\\">\\n          Enabling Developer Mode in Chrome\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q6\\\" value=\\\"LoadUnpacked\\\">\\n          Using \\\"Load unpacked\\\" in the Extensions Manager\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q6\\\" value=\\\"VPNMandatory\\\" data-correct=\\\"true\\\">\\n          Having a VPN enabled during installation\\n        </label>\\n      </div>\\n\\n      <!-- Q7 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>7. Where do you find the extracted emails & phone numbers using SalesQL?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"InsideProfile\\\">\\n          Inside the LinkedIn profile\\u2019s native Contact Info section\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"SalesQLExtIcon\\\" data-correct=\\\"true\\\">\\n          By clicking \\\"Reveal info &amp; Add\\\" within the SalesQL extension window\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"LinkedInJobs\\\">\\n          In LinkedIn\\u2019s \\\"Jobs\\\" tab\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"CompanyWeb\\\">\\n          Directly on the company\\u2019s official website\\n        </label>\\n      </div>\\n\\n      <!-- Q8 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>8. Which SalesQL feature is especially valuable for lead generation?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"RevealWithoutConnect\\\" data-correct=\\\"true\\\">\\n          Revealing contact details without a LinkedIn connection\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"RemovePosts\\\">\\n          Removing spam posts on LinkedIn\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"AutoJobs\\\">\\n          Automating LinkedIn Jobs listings\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"CRMEmbedded\\\">\\n          Creating an embedded CRM inside LinkedIn\\n        </label>\\n      </div>\\n\\n      <!-- Q9 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>9. How do SalesQL credits work in the free plan?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q9\\\" value=\\\"NoLimit\\\">\\n          Unlimited credits for everyone\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q9\\\" value=\\\"50PerMonth\\\" data-correct=\\\"true\\\">\\n          50 credits per month, each credit reveals one lead\\u2019s contact details\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q9\\\" value=\\\"DailyRefill\\\">\\n          10 credits per day, refilled daily\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q9\\\" value=\\\"PurchaseOneByOne\\\">\\n          You must purchase each lead\\u2019s info individually\\n        </label>\\n      </div>\\n\\n      <!-- Q10 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>10. If a lead\\u2019s email isn\\u2019t found in SalesQL, what is a recommended next step?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"GiveUp\\\">\\n          Abandon the lead and never contact them\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"TryDataScraping\\\" data-correct=\\\"true\\\">\\n          Use data scraping tools or alternative email finders\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"ConfrontUser\\\">\\n          Message the user aggressively on LinkedIn about their hidden info\\n        </label>\\n        <label><input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"BribeWithCredits\\\">\\n          Bribe the lead with your leftover SalesQL credits\\n        </label>\\n      </div>\\n\\n      <!-- Q11 (Multi Answer) -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>11. Select all recommended ways to install SalesQL.</h3>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q11\\\" value=\\\"ChromeWeb\\\" data-correct=\\\"true\\\">\\n          Through the Chrome Web Store (standard installation)\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q11\\\" value=\\\"ManualInstall\\\" data-correct=\\\"true\\\">\\n          Manual installation by downloading the ZIP and loading unpacked\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q11\\\" value=\\\"iPhoneApp\\\">\\n          Downloading the iPhone-only version of SalesQL\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q11\\\" value=\\\"BetaTesters\\\">\\n          A closed beta test program found on social media\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q11\\\" value=\\\"AutoSystem\\\">\\n          Automatic system scripts with no user interaction\\n        </label>\\n      </div>\\n\\n      <!-- Q12 (Multi Answer) -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>12. Select all necessary steps when using the SalesQL extension on a LinkedIn profile.</h3>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q12\\\" value=\\\"OpenProfile\\\" data-correct=\\\"true\\\">\\n          Open the LinkedIn profile or search page\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q12\\\" value=\\\"ClickSalesQL\\\" data-correct=\\\"true\\\">\\n          Click the SalesQL icon to activate it\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q12\\\" value=\\\"AcptTOS\\\" data-correct=\\\"true\\\">\\n          Accept the Terms &amp; Policy (on first-time use)\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q12\\\" value=\\\"Wait30Days\\\">\\n          Wait 30 days before revealing info\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q12\\\" value=\\\"RevealAdd\\\" data-correct=\\\"true\\\">\\n          Click \\\"Reveal info &amp; Add\\\" to see verified emails/phones\\n        </label>\\n      </div>\\n\\n      <!-- Q13 (Multi Answer) -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>13. Select all best practices for efficient lead generation using SalesQL.</h3>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q13\\\" value=\\\"VerifiedEmails\\\" data-correct=\\\"true\\\">\\n          Extract verified emails &amp; phone numbers\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q13\\\" value=\\\"BlastEveryone\\\">\\n          Reveal every contact to build a massive, unfiltered list\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q13\\\" value=\\\"PrioritizeDecisionMakers\\\" data-correct=\\\"true\\\">\\n          Use credits on decision-makers &amp; high-potential leads first\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q13\\\" value=\\\"IgnoreCRM\\\">\\n          Skip transferring data into a CRM\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q13\\\" value=\\\"UseSearch\\\">\\n          Rely on LinkedIn search results for bulk extraction\\n        </label>\\n      </div>\\n\\n      <!-- Q14 (Multi Answer) -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>14. Which advanced features can you access from the SalesQL dashboard?</h3>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q14\\\" value=\\\"GuidedTour\\\" data-correct=\\\"true\\\">\\n          A guided tour explaining profile search &amp; extracting contact info\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q14\\\" value=\\\"DNSLeak\\\">\\n          DNS leak testing for your network\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q14\\\" value=\\\"ExportCSV\\\" data-correct=\\\"true\\\">\\n          Exporting leads to a CSV file\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q14\\\" value=\\\"PredictiveDial\\\">\\n          Integrated predictive dialer for phone calls\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q14\\\" value=\\\"PaidLeadAnalytics\\\">\\n          Tools to analyze competitor ad spend\\n        </label>\\n      </div>\\n\\n      <!-- Q15 (Multi Answer) -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>15. Which items represent common issues and solutions with SalesQL usage?</h3>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q15\\\" value=\\\"ExtnNotWorking\\\" data-correct=\\\"true\\\">\\n          Issue: Extension not working \\u2192 Solution: Refresh LinkedIn &amp; restart Chrome\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q15\\\" value=\\\"NoFreePlan\\\">\\n          Issue: No free plan available \\u2192 Solution: Must pay from day one\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q15\\\" value=\\\"NoContactsAvail\\\" data-correct=\\\"true\\\">\\n          Issue: Can\\u2019t find email or phone \\u2192 Solution: Some profiles have no public info\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q15\\\" value=\\\"ManualNeeded\\\" data-correct=\\\"true\\\">\\n          Issue: Need manual installation \\u2192 Solution: Follow the ZIP extraction steps\\n        </label>\\n        <label><input type=\\\"checkbox\\\" name=\\\"q15\\\" value=\\\"NoSupport\\\">\\n          Issue: Zero support from developer \\u2192 Solution: Switch to a different browser\\n        </label>\\n      </div>\\n\\n      <!-- Submit Exam -->\\n      <button id=\\\"submitExamBtn\\\" type=\\\"button\\\" onclick=\\\"calculateScore()\\\">Submit Exam</button>\\n    </form>\\n    <div id=\\\"result\\\"></div>\\n  </div>\\n\\n  <script>\\n    let timer;\\n    let seconds = 0;\\n    const timeLimit = 300; // 5 minutes\\n    const examName = \\\"Collect contacts via SalesQL\\\";\\n\\n    function startQuiz() {\\n      document.getElementById('titlePage').classList.add('hidden');\\n      document.getElementById('quizContainer').classList.remove('hidden');\\n      startTimer();\\n    }\\n\\n    function startTimer() {\\n      timer = setInterval(() => {\\n        seconds++;\\n        const minutes = Math.floor(seconds / 60);\\n        const remaining = seconds % 60;\\n        document.getElementById('timer').textContent =\\n          String(minutes).padStart(2, '0') + \\\":\\\" + String(remaining).padStart(2, '0');\\n\\n        if (seconds >= timeLimit) {\\n          clearInterval(timer);\\n          calculateScore(true);\\n        }\\n      }, 1000);\\n    }\\n\\n    function calculateScore(timeout = false) {\\n      // Hide submit button\\n      const submitBtn = document.getElementById('submitExamBtn');\\n      if (submitBtn) {\\n        submitBtn.style.display = 'none';\\n      }\\n\\n      clearInterval(timer);\\n\\n      let totalPoints = 0;\\n      let maxPoints = 0;\\n      const questions = document.querySelectorAll('.question');\\n\\n      questions.forEach(question => {\\n        const points = parseInt(question.dataset.points) || 1;\\n        maxPoints += points;\\n        // skip user data block if data-points=\\\"0\\\"\\n        if (points === 0) return;\\n\\n        let correct = true;\\n        const inputs = question.querySelectorAll('input');\\n        let selectedValues = [];\\n        let correctValues = [];\\n\\n        inputs.forEach(input => {\\n          if (input.checked) {\\n            selectedValues.push(input.value);\\n          }\\n          if (input.dataset.correct === \\\"true\\\") {\\n            correctValues.push(input.value);\\n          }\\n        });\\n\\n        if (\\n          selectedValues.length !== correctValues.length ||\\n          !selectedValues.every(val => correctValues.includes(val))\\n        ) {\\n          correct = false;\\n        }\\n\\n        if (correct) {\\n          totalPoints += points;\\n          question.classList.add('correct');\\n        } else {\\n          question.classList.add('incorrect');\\n          // Highlight correct answers\\n          inputs.forEach(input => {\\n            if (input.dataset.correct === \\\"true\\\") {\\n              input.parentElement.classList.add('correct-answer');\\n            }\\n          });\\n        }\\n      });\\n\\n      const percentage = ((totalPoints / maxPoints) * 100).toFixed(1);\\n      const timeUsedSec = seconds;\\n      const timeUsed = `${Math.floor(timeUsedSec / 60)}m ${timeUsedSec % 60}s`;\\n\\n      let resultHTML = `\\n        <h3>Exam Results</h3>\\n        <p>Your score: ${totalPoints} / ${maxPoints} (${percentage}%)</p>\\n        <p>Time used: ${timeUsed}</p>\\n        ${\\n          percentage >= 80\\n            ? \\\"Congratulations! You passed!\\\"\\n            : \\\"Try again! Review your mistakes below.\\\"\\n        }\\n        ${timeout ? \\\"<p class='warning'>Time limit exceeded!</p>\\\" : \\\"\\\"}\\n      `;\\n      // Add \\\"Retry\\\" and \\\"Read Again\\\" buttons\\n      resultHTML += `\\n        <button type=\\\"button\\\" onclick=\\\"location.reload()\\\">Retry</button>\\n        <button type=\\\"button\\\" onclick=\\\"window.location.href='https://hook.eu1.make.com/jcjl176nvrhlm4ag7qiixjzssxqdbl5n'\\\">Read Again</button>\\n      `;\\n\\n      document.getElementById('result').innerHTML = resultHTML;\\n\\n      // Gather user data\\n      const fullName = document.querySelector('[name=\\\"fullName\\\"]')?.value.trim() || \\\"\\\";\\n      const profession = document.querySelector('[name=\\\"profession\\\"]')?.value.trim() || \\\"\\\";\\n      const recruiter = document.querySelector('[name=\\\"recruiter\\\"]')?.value.trim() || \\\"\\\";\\n\\n      const postData = {\\n        fullName,\\n        profession,\\n        recruiter,\\n        day: \\\"4\\\",\\n        examName,\\n        scoreObtained: totalPoints,\\n        scoreTotal: maxPoints,\\n        timeUsed: timeUsedSec,\\n        timeTotal: timeLimit\\n      };\\n\\n      fetch(\\\"https://hook.eu1.make.com/2yp4rtezjxi2o6fi2jouedn3i7sgxss1\\\", {\\n        method: \\\"POST\\\",\\n        headers: { \\\"Content-Type\\\": \\\"application/json\\\" },\\n        body: JSON.stringify(postData)\\n      })\\n      .then(res => res.text())\\n      .then(data => console.log(\\\"POST response:\\\", data))\\n      .catch(err => console.error(\\\"Error in POST:\\\", err));\\n\\n      window.scrollTo({ top: 0, behavior: 'smooth' });\\n    }\\n  </script>\\n</body>\\n</html>\\n\", \"options\": {\"responseCode\": 200, \"responseHeaders\": {\"entries\": [{\"name\": \"Content-type\", \"value\": \"text/html\"}]}}}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1.1, \"position\": [220, 0]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"AppTest Collect contacts via SalesQL\", \"nodes\": [{\"parameters\": {\"path\": \"app-test-collect-contacts-via-salesql\", \"method\": \"GET\", \"response\": \"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n  <meta charset=\\\"UTF-8\\\">\\n  <title>AppTest Collect contacts via SalesQL</title>\\n  <style>\\n    body {\\n      font-family: Arial, sans-serif;\\n      max-width: 800px;\\n      margin: 0 auto;\\n      padding: 20px;\\n    }\\n    h1 {\\n      text-align: center;\\n    }\\n    .timer {\\n      text-align: center;\\n      font-weight: bold;\\n      margin: 20px 0;\\n    }\\n    form {\\n      background: #f9f9f9;\\n      padding: 20px;\\n      border-radius: 8px;\\n    }\\n    label {\\n      display: block;\\n      margin: 10px 0;\\n    }\\n    input[type=\\\"text\\\"], input[type=\\\"email\\\"] {\\n      width: 100%;\\n      padding: 8px;\\n      margin-top: 5px;\\n      box-sizing: border-box;\\n    }\\n    input[type=\\\"radio\\\"], input[type=\\\"checkbox\\\"] {\\n      margin-right: 5px;\\n    }\\n    button {\\n      display: block;\\n      margin: 20px auto;\\n      padding: 10px 20px;\\n      background: #4CAF50;\\n      color: white;\\n      border: none;\\n      border-radius: 4px;\\n      cursor: pointer;\\n    }\\n    button:hover {\\n      background: #45a049;\\n    }\\n    .result {\\n      text-align: center;\\n      font-weight: bold;\\n      margin-top: 20px;\\n    }\\n    .correct {\\n      background: #d4edda;\\n      border: 1px solid #c3e6cb;\\n      padding: 5px;\\n      border-radius: 4px;\\n    }\\n    .incorrect {\\n      background: #f8d7da;\\n      border: 1px solid #f5c6cb;\\n      padding: 5px;\\n      border-radius: 4px;\\n    }\\n  </style>\\n</head>\\n<body>\\n  <h1>AppTest Collect contacts via SalesQL</h1>\\n  <div class=\\\"timer\\\">Time left: <span id=\\\"timer\\\">300</span>s</div>\\n  <form id=\\\"quizForm\\\">\\n    <label>1. What is SalesQL primarily used for? (Single Answer)\\n      <input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) Collecting contact data\\n      <input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"B\\\"> B) Managing customer relationships\\n      <input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"C\\\"> C) Creating marketing campaigns\\n      <input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"D\\\"> D) Analyzing sales data\\n    </label>\\n\\n    <label>2. Which of the following is NOT a feature of SalesQL? (Single Answer)\\n      <input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) Email tracking\\n      <input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"B\\\"> B) Contact discovery\\n      <input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"C\\\"> C) Social media monitoring\\n      <input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"D\\\"> D) Lead scoring\\n    </label>\\n\\n    <label>3. How does SalesQL help in lead generation? (Multi Answer)\\n      <input type=\\\"checkbox\\\" name=\\\"q3\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) By finding decision-makers\\n      <input type=\\\"checkbox\\\" name=\\\"q3\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> B) By identifying company contacts\\n      <input type=\\\"checkbox\\\" name=\\\"q3\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> C) By analyzing company data\\n      <input type=\\\"checkbox\\\" name=\\\"q3\\\" value=\\\"D\\\"> D) By creating marketing content\\n    </label>\\n\\n    <label>4. What is the primary benefit of using SalesQL for sales teams? (Single Answer)\\n      <input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) Saves time on lead research\\n      <input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"B\\\"> B) Increases email open rates\\n      <input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"C\\\"> C) Automates social media posts\\n      <input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"D\\\"> D) Tracks customer feedback\\n    </label>\\n\\n    <label>5. Which of the following is a key component of SalesQL's lead discovery process? (Single Answer)\\n      <input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) Email discovery\\n      <input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"B\\\"> B) Social media analytics\\n      <input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"C\\\"> C) Email marketing automation\\n      <input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"D\\\"> D) CRM integration\\n    </label>\\n\\n    <label>6. How does SalesQL integrate with other tools? (Multi Answer)\\n      <input type=\\\"checkbox\\\" name=\\\"q6\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) Email clients\\n      <input type=\\\"checkbox\\\" name=\\\"q6\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> B) CRM platforms\\n      <input type=\\\"checkbox\\\" name=\\\"q6\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> C) Marketing automation tools\\n      <input type=\\\"checkbox\\\" name=\\\"q6\\\" value=\\\"D\\\"> D) Social media platforms\\n    </label>\\n\\n    <label>7. What is the purpose of SalesQL's email discovery feature? (Single Answer)\\n      <input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) To find company emails\\n      <input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"B\\\"> B) To send personalized emails\\n      <input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"C\\\"> C) To track email engagement\\n      <input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"D\\\"> D) To automate email campaigns\\n    </label>\\n\\n    <label>8. Which of the following is NOT a benefit of SalesQL's email discovery? (Single Answer)\\n      <input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) Saves time on manual research\\n      <input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"B\\\"> B) Increases lead quality\\n      <input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"C\\\"> C) Reduces email spam\\n      <input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"D\\\"> D) Increases email open rates\\n    </label>\\n\\n    <label>9. How does SalesQL help in identifying company contacts? (Multi Answer)\\n      <input type=\\\"checkbox\\\" name=\\\"q9\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) By analyzing company websites\\n      <input type=\\\"checkbox\\\" name=\\\"q9\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> B) By searching social media profiles\\n      <input type=\\\"checkbox\\\" name=\\\"q9\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> C) By using email discovery\\n      <input type=\\\"checkbox\\\" name=\\\"q9\\\" value=\\\"D\\\"> D) By tracking customer feedback\\n    </label>\\n\\n    <label>10. What is the primary goal of SalesQL's lead scoring feature? (Single Answer)\\n      <input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) To prioritize leads based on potential\\n      <input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"B\\\"> B) To track lead engagement\\n      <input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"C\\\"> C) To automate lead follow-ups\\n      <input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"D\\\"> D) To increase lead conversion rates\\n    </label>\\n\\n    <label>11. Which of the following is a key benefit of SalesQL's lead scoring? (Single Answer)\\n      <input type=\\\"radio\\\" name=\\\"q11\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) Saves time on manual lead prioritization\\n      <input type=\\\"radio\\\" name=\\\"q11\\\" value=\\\"B\\\"> B) Increases email open rates\\n      <input type=\\\"radio\\\" name=\\\"q11\\\" value=\\\"C\\\"> C) Automates social media posts\\n      <input type=\\\"radio\\\" name=\\\"q11\\\" value=\\\"D\\\"> D) Tracks customer feedback\\n    </label>\\n\\n    <label>12. How does SalesQL integrate with CRM platforms? (Multi Answer)\\n      <input type=\\\"checkbox\\\" name=\\\"q12\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) By syncing lead data\\n      <input type=\\\"checkbox\\\" name=\\\"q12\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> B) By updating lead status\\n      <input type=\\\"checkbox\\\" name=\\\"q12\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> C) By tracking lead engagement\\n      <input type=\\\"checkbox\\\" name=\\\"q12\\\" value=\\\"D\\\"> D) By automating lead follow-ups\\n    </label>\\n\\n    <label>13. What is the purpose of SalesQL's social media monitoring feature? (Single Answer)\\n      <input type=\\\"radio\\\" name=\\\"q13\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) To track company activity on social media\\n      <input type=\\\"radio\\\" name=\\\"q13\\\" value=\\\"B\\\"> B) To increase social media engagement\\n      <input type=\\\"radio\\\" name=\\\"q13\\\" value=\\\"C\\\"> C) To create social media content\\n      <input type=\\\"radio\\\" name=\\\"q13\\\" value=\\\"D\\\"> D) To track customer feedback\\n    </label>\\n\\n    <label>14. Which of the following is NOT a benefit of SalesQL's social media monitoring? (Single Answer)\\n      <input type=\\\"radio\\\" name=\\\"q14\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) Saves time on manual research\\n      <input type=\\\"radio\\\" name=\\\"q14\\\" value=\\\"B\\\"> B) Increases lead quality\\n      <input type=\\\"radio\\\" name=\\\"q14\\\" value=\\\"C\\\"> C) Increases social media engagement\\n      <input type=\\\"radio\\\" name=\\\"q14\\\" value=\\\"D\\\"> D) Increases email open rates\\n    </label>\\n\\n    <label>15. How does SalesQL help in automating lead follow-ups? (Multi Answer)\\n      <input type=\\\"checkbox\\\" name=\\\"q15\\\" value=\\\"A\\\" data-correct=\\\"true\\\"> A) By sending automated emails\\n      <input type=\\\"checkbox\\\" name=\\\"q15\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> B) By tracking lead engagement\\n      <input type=\\\"checkbox\\\" name=\\\"q15\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> C) By updating lead status in CRM\\n      <input type=\\\"checkbox\\\" name=\\\"q15\\\" value=\\\"D\\\"> D) By creating marketing content\\n    </label>\\n\\n    <button type=\\\"button\\\" onclick=\\\"calculateScore()\\\">Submit</button>\\n  </form>\\n  <div class=\\\"result\\\" id=\\\"result\\\"></div>\\n  <script>\\n    const timerDuration = 300; // 5 minutes in seconds\\n    let timer;\\n    let timeLeft = timerDuration;\\n    const timerDisplay = document.getElementById('timer');\\n\\n    function startTimer() {\\n      timer = setInterval(() => {\\n        timeLeft--;\\n        timerDisplay.textContent = timeLeft;\\n        if (timeLeft <= 0) {\\n          clearInterval(timer);\\n          calculateScore();\\n        }\\n      }, 1000);\\n    }\\n\\n    function calculateScore() {\\n      clearInterval(timer);\\n      const questions = document.querySelectorAll('input[type=\\\"radio\\\"]:checked, input[type=\\\"checkbox\\\"]:checked');\\n      let correct = 0;\\n      let total = 0;\\n      questions.forEach(q => {\\n        const correctValue = q.closest('label').dataset.correct;\\n        if (correctValue && q.value === correctValue) {\\n          correct++;\\n        }\\n        total++;\\n      });\\n      const score = ((correct / total) * 100).toFixed(2);\\n      const resultDiv = document.getElementById('result');\\n      resultDiv.innerHTML = `You scored ${score}%. <br>${score >= 80 ? 'Congratulations! You passed!' : 'Try again!'}`;\\n      const quizData = {\\n        name: document.querySelector('input[name=\\\"name\\\"]').value,\\n        email: document.querySelector('input[name=\\\"email\\\"]').value,\\n        quizName: 'AppTest Collect contacts via SalesQL',\\n        correct: correct,\\n        total: total,\\n        score: score,\\n        timeTaken: timerDuration - timeLeft\\n      };\\n      fetch('https://hook.eu1.make.com/your-webhook-url', {\\n        method: 'POST',\\n        headers: {'Content-Type': 'application/json'},\\n        body: JSON.stringify(quizData)\\n      });\\n    }\\n\\n    startTimer();\\n  </script>\\n</body>\\n</html>\"}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [0, 0]}], \"connections\": {}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"options\": {}}, \"name\": \"When chat message received\", \"type\": \"@n8n/n8n-nodes-langchain.chatTrigger\", \"typeVersion\": 1.1, \"position\": [-880, 360]}, {\"parameters\": {\"options\": {\"systemMessage\": \"You are a cybersecurity expert trained on MITRE ATT&CK and enterprise incident response. Your job is to:\\n1. Extract TTP information from SIEM data.\\n2. Provide actionable remediation steps tailored to the alert.\\n3. Cross-reference historical patterns and related alerts.\\n4. Recommend external resources for deeper understanding.\\n\\nEnsure that:\\n- TTPs are tagged with the tactic, technique name, and technique ID.\\n- Remediation steps are specific and actionable.\\n- Historical data includes related alerts and notable trends.\\n- External links are relevant to the observed behavior.\\n\"}}, \"name\": \"AI Agent\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.7, \"position\": [-680, 360]}, {\"parameters\": {\"model\": \"gpt-4o\", \"options\": {}}, \"name\": \"OpenAI Chat Model\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatOpenAi\", \"typeVersion\": 1, \"position\": [-740, 580]}, {\"parameters\": {\"options\": {}, \"fieldToSplitOut\": \"data\"}, \"name\": \"Split Out\", \"type\": \"n8n-nodes-base.splitOut\", \"typeVersion\": 1, \"position\": [-720, 1220]}, {\"parameters\": {\"options\": {}}, \"name\": \"Embeddings OpenAI1\", \"type\": \"@n8n/n8n-nodes-langchain.embeddingsOpenAi\", \"typeVersion\": 1.2, \"position\": [-580, 1420]}, {\"parameters\": {\"options\": {\"metadata\": {\"metadataValues\": [{\"name\": \"id\", \"value\": \"={{ $json.id }}\"}, {\"name\": \"name\", \"value\": \"={{ $json.name }}\"}, {\"name\": \"killchain\", \"value\": \"={{ $json.kill_chain_phases }}\"}, {\"name\": \"external\", \"value\": \"={{ $json.external_references }}\"}]}}, \"jsonData\": \"={{ $json.description }}\", \"jsonMode\": \"expressionData\"}, \"name\": \"Default Data Loader\", \"type\": \"@n8n/n8n-nodes-langchain.documentDefaultDataLoader\", \"typeVersion\": 1, \"position\": [-460, 1440]}, {\"parameters\": {}, \"name\": \"Token Splitter1\", \"type\": \"@n8n/n8n-nodes-langchain.textSplitterTokenSplitter\", \"typeVersion\": 1, \"position\": [-460, 1620]}, {\"parameters\": {}, \"name\": \"Window Buffer Memory\", \"type\": \"@n8n/n8n-nodes-langchain.memoryBufferWindow\", \"typeVersion\": 1.3, \"position\": [-580, 580]}, {\"parameters\": {\"model\": \"text-embedding-3-large\", \"options\": {\"dimensions\": 1536}}, \"name\": \"Embeddings OpenAI2\", \"type\": \"@n8n/n8n-nodes-langchain.embeddingsOpenAi\", \"typeVersion\": 1.2, \"position\": [-460, 720]}, {\"parameters\": {\"options\": {}, \"operation\": \"fromJson\"}, \"name\": \"Extract from File\", \"type\": \"n8n-nodes-base.extractFromFile\", \"typeVersion\": 1, \"position\": [-920, 1220]}, {\"parameters\": {}, \"name\": \"When clicking \\u2018Test workflow\\u2019\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [-1360, 1220]}, {\"parameters\": {\"text\": \"=Siem Alert Data:\\nAlert: {{ $json.raw_subject }}\\nDescription: {{ $json.description }}\", \"options\": {\"systemMessage\": \"You are a cybersecurity expert trained on MITRE ATT&CK and enterprise incident response. Your job is to:\\n1. Extract TTP information from SIEM data.\\n2. Provide actionable remediation steps tailored to the alert.\\n3. Cross-reference historical patterns and related alerts.\\n4. Recommend external resources for deeper understanding.\\n\\nEnsure that:\\n- TTPs are tagged with the tactic, technique name, and technique ID.\\n- Remediation steps are specific and actionable.\\n- Historical data includes related alerts and notable trends.\\n- External links are relevant to the observed behavior.\\n\\nPlease output your response in html format, but do not include ```html at the beginning \\n\"}, \"promptType\": \"define\", \"hasOutputParser\": true}, \"name\": \"AI Agent1\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.7, \"position\": [-540, -440]}, {\"parameters\": {\"model\": \"gpt-4o\", \"options\": {}}, \"name\": \"OpenAI Chat Model1\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatOpenAi\", \"typeVersion\": 1, \"position\": [-600, -220]}, {\"parameters\": {\"model\": \"text-embedding-3-large\", \"options\": {\"dimensions\": 1536}}, \"name\": \"Embeddings OpenAI\", \"type\": \"@n8n/n8n-nodes-langchain.embeddingsOpenAi\", \"typeVersion\": 1.2, \"position\": [-320, -40]}, {\"parameters\": {\"options\": {}}, \"name\": \"Loop Over Items\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [-940, -440]}, {\"parameters\": {\"color\": 7, \"width\": 1380, \"height\": 820, \"content\": \"![n8n](https://uploads.n8n.io/templates/qdrantlogo.png)\\n## Embed your Vector Store\\nTo provide data for your Vector store, you need to pass it in as JSON, and ensure it's setup correctly. This flow pulls the JSON file from Google Drive and extracts the JSON data and then passes it into the qdrant collection. \"}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-1460, 940]}, {\"parameters\": {\"color\": 7, \"width\": 1380, \"height\": 680, \"content\": \"![n8n](https://uploads.n8n.io/templates/n8n.png)\\n## Talk to your Vector Store\\nNow that your vector store has been updated with the embedded data, \\nyou can use the n8n chat interface to talk to your data using OpenAI, \\nOllama, or any of our supported LLMs.\"}, \"name\": \"Sticky Note1\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-1460, 220]}, {\"parameters\": {\"color\": 7, \"width\": 2140, \"height\": 900, \"content\": \"![Servicenow](https://uploads.n8n.io/templates/zendesk.png)\\n## Deploy your Vector Store\\nThis flow adds contextual information to your tickets using the Mitre Attack framework to help contextualize the ticket data.\"}, \"name\": \"Sticky Note2\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-1460, -700]}, {\"parameters\": {\"jsonSchemaExample\": \"{\\n  \\\"ttp_identification\\\": {\\n    \\\"alert_summary\\\": \\\"The alert indicates a check-in from the NetSupport RAT, a known Remote Access Trojan, suggesting command and control (C2) communication.\\\",\\n    \\\"mitre_attack_ttps\\\": [\\n      {\\n        \\\"tactic\\\": \\\"Command and Control\\\",\\n        \\\"technique\\\": \\\"Protocol or Service Impersonation\\\",\\n        \\\"technique_id\\\": \\\"T1001.003\\\",\\n        \\\"description\\\": \\\"The RAT's check-in over port 443 implies potential masquerading of its traffic as legitimate SSL/TLS traffic, a tactic often used to blend C2 communications with normal web traffic.\\\",\\n        \\\"reference\\\": \\\"https://attack.mitre.org/techniques/T1001/003/\\\"\\n      }\\n    ]\\n  },\\n  \\\"remediation_steps\\\": {\\n    \\\"network_segmentation\\\": {\\n      \\\"action\\\": \\\"Isolate the affected host\\\",\\n      \\\"target\\\": \\\"************\\\",\\n      \\\"reason\\\": \\\"Prevents further C2 communication or lateral movement.\\\"\\n    },\\n    \\\"endpoint_inspection\\\": {\\n      \\\"action\\\": \\\"Perform a thorough inspection\\\",\\n      \\\"target\\\": \\\"Impacted endpoint\\\",\\n      \\\"method\\\": \\\"Use endpoint detection and response (EDR) tools to check for additional persistence mechanisms.\\\"\\n    },\\n    \\\"network_traffic_analysis\\\": {\\n      \\\"action\\\": \\\"Investigate and block unusual traffic\\\",\\n      \\\"target\\\": \\\"IP **************\\\",\\n      \\\"method\\\": \\\"Implement blocks for the IP across the firewall or IDS/IPS systems.\\\"\\n    },\\n    \\\"system_patching\\\": {\\n      \\\"action\\\": \\\"Ensure all systems are updated\\\",\\n      \\\"method\\\": \\\"Apply the latest security patches to mitigate vulnerabilities exploited by RAT malware.\\\"\\n    },\\n    \\\"ioc_hunting\\\": {\\n      \\\"action\\\": \\\"Search for Indicators of Compromise (IoCs)\\\",\\n      \\\"method\\\": \\\"Check for NetSupport RAT IoCs across other endpoints within the network.\\\"\\n    }\\n  },\\n  \\\"historical_patterns\\\": {\\n    \\\"network_anomalies\\\": \\\"Past alerts involving similar attempts to use standard web ports (e.g., 80, 443) for non-standard applications could suggest a broader attempt to blend malicious traffic into legitimate streams.\\\",\\n    \\\"persistence_tactics\\\": \\\"Any detection of anomalies in task scheduling or shortcut modifications may indicate persistence methods similar to those used by RATs.\\\"\\n  },\\n  \\\"external_resources\\\": [\\n    {\\n      \\\"title\\\": \\\"ESET Report on Okrum and Ketrican\\\",\\n      \\\"description\\\": \\\"Discusses similar tactics involving protocol impersonation and C2.\\\",\\n      \\\"url\\\": \\\"https://www.eset.com/int/about/newsroom/research/okrum-ketrican/\\\"\\n    },\\n    {\\n      \\\"title\\\": \\\"Malleable C2 Profiles\\\",\\n      \\\"description\\\": \\\"Document on crafting custom C2 traffic profiles similar to the targeting methods used by NetSupport RAT.\\\",\\n      \\\"url\\\": \\\"https://www.cobaltstrike.com/help-malleable-c2\\\"\\n    },\\n    {\\n      \\\"title\\\": \\\"MITRE ATT&CK Technique Overview\\\",\\n      \\\"description\\\": \\\"Overview of Protocol or Service Impersonation tactics.\\\",\\n      \\\"url\\\": \\\"https://attack.mitre.org/techniques/T1001/003/\\\"\\n    }\\n  ]\\n}\\n\"}, \"name\": \"Structured Output Parser\", \"type\": \"@n8n/n8n-nodes-langchain.outputParserStructured\", \"typeVersion\": 1.2, \"position\": [0, -160]}, {\"parameters\": {\"fileId\": {\"__rl\": true, \"mode\": \"list\", \"value\": \"1oWBLO5AlIqbgo9mKD1hNtx92HdC6O28d\", \"cachedResultUrl\": \"https://drive.google.com/file/d/1oWBLO5AlIqbgo9mKD1hNtx92HdC6O28d/view?usp=drivesdk\", \"cachedResultName\": \"cleaned_mitre_attack_data.json\"}, \"options\": {}, \"operation\": \"download\"}, \"name\": \"Pull Mitre Data From Gdrive\", \"type\": \"n8n-nodes-base.googleDrive\", \"typeVersion\": 3, \"position\": [-1140, 1220]}, {\"parameters\": {\"mode\": \"insert\", \"options\": {}, \"qdrantCollection\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"mitre\"}}, \"name\": \"Embed JSON in Qdrant Collection\", \"type\": \"@n8n/n8n-nodes-langchain.vectorStoreQdrant\", \"typeVersion\": 1, \"position\": [-520, 1220]}, {\"parameters\": {\"mode\": \"retrieve-as-tool\", \"options\": {}, \"toolName\": \"mitre_attack_vector_store\", \"toolDescription\": \"The mitre_attack_vector_store is a knowledge base trained on the MITRE ATT&CK framework. It is designed to help identify, correlate, and provide context for cybersecurity incidents based on textual descriptions of alerts, events, or behaviors. This tool leverages precomputed embeddings of attack techniques, tactics, and procedures (TTPs) to map user queries (such as SIEM-generated alerts or JIRA ticket titles) to relevant MITRE ATT&CK techniques.\\n\\nBy analyzing input text, the vector store can:\\n\\nRetrieve the most relevant MITRE ATT&CK entries (e.g., techniques, tactics, descriptions, external references).\\nProvide structured context about potential adversary behaviors.\\nSuggest remediation actions or detection methods based on the input.\", \"qdrantCollection\": {\"__rl\": true, \"mode\": \"list\", \"value\": \"mitre\", \"cachedResultName\": \"mitre\"}}, \"name\": \"Query Qdrant Vector Store\", \"type\": \"@n8n/n8n-nodes-langchain.vectorStoreQdrant\", \"typeVersion\": 1, \"position\": [-480, 580]}, {\"parameters\": {\"mode\": \"retrieve-as-tool\", \"options\": {}, \"toolName\": \"mitre_attack_vector_store\", \"toolDescription\": \"The mitre_attack_vector_store is a knowledge base trained on the MITRE ATT&CK framework. It is designed to help identify, correlate, and provide context for cybersecurity incidents based on textual descriptions of alerts, events, or behaviors. This tool leverages precomputed embeddings of attack techniques, tactics, and procedures (TTPs) to map user queries (such as SIEM-generated alerts or JIRA ticket titles) to relevant MITRE ATT&CK techniques.\\n\\nBy analyzing input text, the vector store can:\\n\\nRetrieve the most relevant MITRE ATT&CK entries (e.g., techniques, tactics, descriptions, external references).\\nProvide structured context about potential adversary behaviors.\\nSuggest remediation actions or detection methods based on the input.\", \"qdrantCollection\": {\"__rl\": true, \"mode\": \"list\", \"value\": \"mitre\", \"cachedResultName\": \"mitre\"}}, \"name\": \"Qdrant Vector Store query\", \"type\": \"@n8n/n8n-nodes-langchain.vectorStoreQdrant\", \"typeVersion\": 1, \"position\": [-320, -200]}, {\"parameters\": {\"options\": {}, \"operation\": \"getAll\"}, \"name\": \"Get all Zendesk Tickets\", \"type\": \"n8n-nodes-base.zendesk\", \"typeVersion\": 1, \"position\": [-1180, -440]}, {\"parameters\": {\"id\": \"={{ $('Loop Over Items').item.json.id }}\", \"operation\": \"update\", \"updateFields\": {\"internalNote\": \"=Summary: {{ $json.output.ttp_identification.alert_summary }}\\n\\n\", \"customFieldsUi\": {\"customFieldsValues\": [{\"id\": 34479547176212, \"value\": \"={{ $json.output.ttp_identification.mitre_attack_ttps[0].technique_id }}\"}, {\"id\": 34479570659732, \"value\": \"={{ $json.output.ttp_identification.mitre_attack_ttps[0].tactic }}\"}]}}}, \"name\": \"Update Zendesk with Mitre Data\", \"type\": \"n8n-nodes-base.zendesk\", \"typeVersion\": 1, \"position\": [0, -360]}, {\"parameters\": {}, \"name\": \"Move on to next ticket\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [360, -80]}], \"connections\": {\"AI Agent\": {\"main\": [[]]}, \"AI Agent1\": {\"main\": [[{\"node\": \"Update Zendesk with Mitre Data\", \"type\": \"main\", \"index\": 0}]]}, \"Split Out\": {\"main\": [[{\"node\": \"Embed JSON in Qdrant Collection\", \"type\": \"main\", \"index\": 0}]]}, \"Loop Over Items\": {\"main\": [[], [{\"node\": \"AI Agent1\", \"type\": \"main\", \"index\": 0}]]}, \"Token Splitter1\": {\"ai_textSplitter\": [[{\"node\": \"Default Data Loader\", \"type\": \"ai_textSplitter\", \"index\": 0}]]}, \"Embeddings OpenAI\": {\"ai_embedding\": [[{\"node\": \"Qdrant Vector Store query\", \"type\": \"ai_embedding\", \"index\": 0}]]}, \"Extract from File\": {\"main\": [[{\"node\": \"Split Out\", \"type\": \"main\", \"index\": 0}]]}, \"OpenAI Chat Model\": {\"ai_languageModel\": [[{\"node\": \"AI Agent\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"Embeddings OpenAI1\": {\"ai_embedding\": [[{\"node\": \"Embed JSON in Qdrant Collection\", \"type\": \"ai_embedding\", \"index\": 0}]]}, \"Embeddings OpenAI2\": {\"ai_embedding\": [[{\"node\": \"Query Qdrant Vector Store\", \"type\": \"ai_embedding\", \"index\": 0}]]}, \"OpenAI Chat Model1\": {\"ai_languageModel\": [[{\"node\": \"AI Agent1\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"Default Data Loader\": {\"ai_document\": [[{\"node\": \"Embed JSON in Qdrant Collection\", \"type\": \"ai_document\", \"index\": 0}]]}, \"Window Buffer Memory\": {\"ai_memory\": [[{\"node\": \"AI Agent\", \"type\": \"ai_memory\", \"index\": 0}]]}, \"Move on to next ticket\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Get all Zendesk Tickets\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Structured Output Parser\": {\"ai_outputParser\": [[{\"node\": \"AI Agent1\", \"type\": \"ai_outputParser\", \"index\": 0}]]}, \"Qdrant Vector Store query\": {\"ai_tool\": [[{\"node\": \"AI Agent1\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"Query Qdrant Vector Store\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"When chat message received\": {\"main\": [[{\"node\": \"AI Agent\", \"type\": \"main\", \"index\": 0}]]}, \"Pull Mitre Data From Gdrive\": {\"main\": [[{\"node\": \"Extract from File\", \"type\": \"main\", \"index\": 0}]]}, \"Update Zendesk with Mitre Data\": {\"main\": [[{\"node\": \"Move on to next ticket\", \"type\": \"main\", \"index\": 0}]]}, \"When clicking \\u2018Test workflow\\u2019\": {\"main\": [[{\"node\": \"Pull Mitre Data From Gdrive\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}, {"ground_truth_json": "{\"name\": \"GetOnboardingFileAgentX\", \"nodes\": [{\"parameters\": {\"path\": \"7ab30047-d1c5-4a1a-9e0b-faf6bdd8134c\", \"responseMode\": \"responseNode\", \"options\": {}}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 2, \"position\": [0, 0]}, {\"parameters\": {\"operation\": \"get\", \"documentURL\": \"https://docs.google.com/document/d/1YUx0HHoti7J94SWn2TzzZ3rnC83jD30oi9DLjhlnXd8/view\"}, \"name\": \"Google Docs\", \"type\": \"n8n-nodes-base.googleDocs\", \"typeVersion\": 2, \"position\": [220, 0]}, {\"parameters\": {\"respondWith\": \"text\", \"responseBody\": \"={{ $json.content }}\", \"options\": {}}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1.1, \"position\": [440, 0]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Google Docs\", \"type\": \"main\", \"index\": 0}]]}, \"Google Docs\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": "{\"name\": \"GetOnboardingFileAgentX\", \"nodes\": [{\"parameters\": {\"httpMethod\": \"GET\", \"path\": \"get-onboarding-file-agentx\", \"responseMode\": \"responseNode\"}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 2, \"position\": [0, 0]}, {\"parameters\": {\"operation\": \"get\", \"documentURL\": \"https://docs.google.com/document/d/1YUx0HHoti7J94SWn2TzzZ3rnC83jD30oi9DLjhlnXd8/view\"}, \"name\": \"Google Docs\", \"type\": \"n8n-nodes-base.googleDocs\", \"typeVersion\": 2, \"position\": [220, 0]}, {\"parameters\": {\"respondWith\": \"text\", \"responseBody\": \"={{ $json.content }}\"}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 2, \"position\": [440, 0]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Google Docs\", \"type\": \"main\", \"index\": 0}]]}, \"Google Docs\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}"}, {"ground_truth_json": "{\"name\": \"call analysis\", \"nodes\": [{\"parameters\": {\"modelId\": {\"__rl\": true, \"value\": \"gpt-4o-mini\", \"mode\": \"list\", \"cachedResultName\": \"GPT-4O-MINI\"}, \"messages\": {\"values\": [{\"content\": \"=\\\"Today is \\\" {{ $now }}  \\\"Transcript: \\\" {{  $('OpenAI').item.json.text }}\"}, {\"content\": \"Analyse the given audio or video file based on the transcription and automatically categorize call as sales , onboarding or support. generate a report based on analysis of call based on performance metrics and generate feedback for the team from the call\", \"role\": \"assistant\"}]}, \"options\": {\"temperature\": 0.8}}, \"name\": \"OpenAI1\", \"type\": \"@n8n/n8n-nodes-langchain.openAi\", \"typeVersion\": 1, \"position\": [380, 0]}, {\"parameters\": {\"resource\": \"audio\", \"operation\": \"transcribe\", \"binaryPropertyName\": \"data0\", \"options\": {}}, \"name\": \"OpenAI\", \"type\": \"@n8n/n8n-nodes-langchain.openAi\", \"typeVersion\": 1, \"position\": [160, 0]}, {\"parameters\": {\"public\": true, \"options\": {\"allowFileUploads\": true}}, \"name\": \"When chat message received\", \"type\": \"@n8n/n8n-nodes-langchain.chatTrigger\", \"typeVersion\": 1.1, \"position\": [-40, 0]}, {\"parameters\": {\"operation\": \"appendOrUpdate\", \"documentId\": {\"__rl\": true, \"value\": \"1TcVF46aWeg7IGQZQVgVbHBUUZhvQAA1fqiRz52_NP9o\", \"mode\": \"list\", \"cachedResultName\": \"call analysis \", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1TcVF46aWeg7IGQZQVgVbHBUUZhvQAA1fqiRz52_NP9o/edit?usp=drivesdk\"}, \"sheetName\": {\"__rl\": true, \"value\": \"gid=0\", \"mode\": \"list\", \"cachedResultName\": \"Sheet1\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1TcVF46aWeg7IGQZQVgVbHBUUZhvQAA1fqiRz52_NP9o/edit#gid=0\"}, \"columns\": {\"mappingMode\": \"defineBelow\", \"value\": {\"call\": \"={{ $('When chat message received').item.json.files[0].fileName }}\", \"analysis \": \"={{ $json.message.content }}\"}, \"matchingColumns\": [\"call\"], \"schema\": [{\"id\": \"call\", \"displayName\": \"call\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"analysis \", \"displayName\": \"analysis \", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true}], \"attemptToConvertTypes\": false, \"convertFieldsToString\": false}, \"options\": {}}, \"name\": \"Google Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.5, \"position\": [740, 0]}], \"connections\": {\"OpenAI1\": {\"main\": [[{\"node\": \"Google Sheets\", \"type\": \"main\", \"index\": 0}]]}, \"OpenAI\": {\"main\": [[{\"node\": \"OpenAI1\", \"type\": \"main\", \"index\": 0}]]}, \"When chat message received\": {\"main\": [[{\"node\": \"OpenAI\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "generated_json": ""}]