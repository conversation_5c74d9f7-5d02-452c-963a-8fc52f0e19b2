import json
import seaborn as sns
import matplotlib.pyplot as plt
import pandas as pd

# Load result
with open("count_validation_results.json", "r") as f:
    result = json.load(f)

same_scores = result["same_sample_scores"]
diff_scores = result["different_sample_scores"]

# Convert to DataFrame
plot_data = pd.DataFrame(
    {"score": same_scores + diff_scores,
     "type": ["Same Sample"] * len(same_scores) + ["Different Sample"] * len(diff_scores)}
)

# Plot
sns.set(style="whitegrid")
sns.histplot(data=plot_data, x="score", hue="type", bins=30, kde=True, palette="Set2")

plt.xlabel("Cosine Similarity Score")
plt.ylabel("Frequency")
plt.legend(title="Comparison Type")
plt.tight_layout()
plt.show()


import json
import seaborn as sns
import matplotlib.pyplot as plt
import pandas as pd

# Load result
with open("tfidf_validation_results.json", "r") as f:
    result = json.load(f)

same_scores = result["same_sample_scores"]
diff_scores = result["different_sample_scores"]

# Convert to DataFrame
plot_data = pd.DataFrame(
    {"score": same_scores + diff_scores,
     "type": ["Same Sample"] * len(same_scores) + ["Different Sample"] * len(diff_scores)}
)

# Plot
sns.set(style="whitegrid")
sns.histplot(data=plot_data, x="score", hue="type", bins=30, kde=True, palette="Set2")

plt.xlabel("Cosine Similarity Score")
plt.ylabel("Frequency")
plt.legend(title="Comparison Type")
plt.tight_layout()
plt.show()


import json
import seaborn as sns
import matplotlib.pyplot as plt
import pandas as pd

# Load result
with open("dict_validation_results.json", "r") as f:
    result = json.load(f)

same_scores = result["same_sample_scores"]
diff_scores = result["different_sample_scores"]

# Convert to DataFrame
plot_data = pd.DataFrame(
    {"score": same_scores + diff_scores,
     "type": ["Same Sample"] * len(same_scores) + ["Different Sample"] * len(diff_scores)}
)

# Plot
sns.set(style="whitegrid")
sns.histplot(data=plot_data, x="score", hue="type", bins=30, kde=True, palette="Set2")

plt.xlabel("Cosine Similarity Score")
plt.ylabel("Frequency")
plt.legend(title="Comparison Type")
plt.tight_layout()
plt.show()


import json
import seaborn as sns
import matplotlib.pyplot as plt
import pandas as pd

# Load result
with open("sbert_validation_results.json", "r") as f:
    result = json.load(f)

same_scores = result["same_sample_scores"]
diff_scores = result["different_sample_scores"]

# Convert to DataFrame
plot_data = pd.DataFrame(
    {"score": same_scores + diff_scores,
     "type": ["Same Sample"] * len(same_scores) + ["Different Sample"] * len(diff_scores)}
)

# Plot
sns.set(style="whitegrid")
sns.histplot(data=plot_data, x="score", hue="type", bins=30, kde=True, palette="Set2")

plt.xlabel("Cosine Similarity Score")
plt.ylabel("Frequency")
plt.legend(title="Comparison Type")
plt.tight_layout()
plt.show()


import json
import seaborn as sns
import matplotlib.pyplot as plt
import pandas as pd

# Load result
with open("all-distilroberta-v1_validation_results.json", "r") as f:
    result = json.load(f)

same_scores = result["same_sample_scores"]
diff_scores = result["different_sample_scores"]

# Convert to DataFrame
plot_data = pd.DataFrame(
    {"score": same_scores + diff_scores,
     "type": ["Same Sample"] * len(same_scores) + ["Different Sample"] * len(diff_scores)}
)

# Plot
sns.set(style="whitegrid")
sns.histplot(data=plot_data, x="score", hue="type", bins=30, kde=True, palette="Set2")

plt.xlabel("Cosine Similarity Score")
plt.ylabel("Frequency")
plt.legend(title="Comparison Type")
plt.tight_layout()
plt.show()


import json
import seaborn as sns
import matplotlib.pyplot as plt
import pandas as pd

# Load result
with open("all-MiniLM-L6-v2_validation_results.json", "r") as f:
    result = json.load(f)

same_scores = result["same_sample_scores"]
diff_scores = result["different_sample_scores"]

# Convert to DataFrame
plot_data = pd.DataFrame(
    {"score": same_scores + diff_scores,
     "type": ["Same Sample"] * len(same_scores) + ["Different Sample"] * len(diff_scores)}
)

# Plot
sns.set(style="whitegrid")
sns.histplot(data=plot_data, x="score", hue="type", bins=30, kde=True, palette="Set2")

plt.xlabel("Cosine Similarity Score")
plt.ylabel("Frequency")
plt.legend(title="Comparison Type")
plt.tight_layout()
plt.show()


import json
import seaborn as sns
import matplotlib.pyplot as plt
import pandas as pd

# Load result
with open("all-MiniLM-L12-v2_validation_results.json", "r") as f:
    result = json.load(f)

same_scores = result["same_sample_scores"]
diff_scores = result["different_sample_scores"]

# Convert to DataFrame
plot_data = pd.DataFrame(
    {"score": same_scores + diff_scores,
     "type": ["Same Sample"] * len(same_scores) + ["Different Sample"] * len(diff_scores)}
)

# Plot
sns.set(style="whitegrid")
sns.histplot(data=plot_data, x="score", hue="type", bins=30, kde=True, palette="Set2")

plt.xlabel("Cosine Similarity Score")
plt.ylabel("Frequency")
plt.legend(title="Comparison Type")
plt.tight_layout()
plt.show()


import json
import seaborn as sns
import matplotlib.pyplot as plt
import pandas as pd

# Load result
with open("all-mpnet-base-v2_validation_results.json", "r") as f:
    result = json.load(f)

same_scores = result["same_sample_scores"]
diff_scores = result["different_sample_scores"]

# Convert to DataFrame
plot_data = pd.DataFrame(
    {"score": same_scores + diff_scores,
     "type": ["Same Sample"] * len(same_scores) + ["Different Sample"] * len(diff_scores)}
)

# Plot
sns.set(style="whitegrid")
sns.histplot(data=plot_data, x="score", hue="type", bins=30, kde=True, palette="Set2")

plt.xlabel("Cosine Similarity Score")
plt.ylabel("Frequency")
plt.legend(title="Comparison Type")
plt.tight_layout()
plt.show()


