import json

from sentence_transformer_validation_function import nested_json_sbert_similarity


# --------------------
# Main script
# --------------------
input_file = "sample_similarity_jsons.json"  # Input JSONL file
output_file = "similarity_results.json"  # Output JSON file
attribute_1 = "ground_truth_json"  # Replace with the key name of the first JSON string
attribute_2 = "generated_json"  # Replace with the key name of the second JSON string

results = []
with open(input_file, "r", encoding="utf-8") as f:
    data = json.load(f)  # Load the list of dictionaries

for record in data:
    if not record.get(attribute_2):  # Skip if generated_json is empty
        continue
    # Parse the JSON strings
    json1 = json.loads(record[attribute_1])
    json2 = json.loads(record[attribute_2])

    # Compute similarity
    similarity_score = float(nested_json_sbert_similarity(json1, json2))

    # Append to results
    results.append(similarity_score)


# Save results
with open(output_file, "w", encoding="utf-8") as f:
    json.dump(results, f, indent=4)

print(f"Saved {len(results)} similarity scores to {output_file}")
