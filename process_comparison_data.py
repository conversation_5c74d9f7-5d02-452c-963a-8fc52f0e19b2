import json
import random
from typing import Any, Dict, List


def load_jsonl_data(file_path: str) -> List[Dict[str, Any]]:
    """Load JSONL data from file"""
    data = []
    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            line = line.strip()
            if line:
                data.append(json.loads(line))
    return data


def load_and_filter_data(file_path: str) -> tuple[List[Dict[str, Any]], List[int]]:
    """
    Load JSON data and filter out samples with empty generated_json
    Returns both filtered data and original indices
    """
    with open(file_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    # Filter out samples with empty generated_json and keep track of original indices
    filtered_data = []
    original_indices = []

    for i, sample in enumerate(data):
        if sample.get("generated_json", "").strip() != "":
            filtered_data.append(sample)
            original_indices.append(i)

    print(f"Original samples: {len(data)}")
    print(f"Filtered samples (with generated_json): {len(filtered_data)}")

    return filtered_data, original_indices


def create_comparison_samples(
    filtered_data: List[Dict[str, Any]],
    prompts_data: List[Dict[str, Any]],
    original_indices: List[int],
) -> List[Dict[str, Any]]:
    """
    Create comparison samples with both same and different pairs using actual prompts from JSONL
    """
    comparison_samples = []

    # Create same samples (ground_truth_json vs generated_json from same sample)
    for i, sample in enumerate(filtered_data):
        original_index = original_indices[i]
        prompt = (
            prompts_data[original_index]["prompt"]
            if original_index < len(prompts_data)
            else f"Compare the ground truth workflow with the generated workflow for sample {i+1}"
        )

        same_comparison = {
            "prompt": prompt,
            "original_json": sample["ground_truth_json"],
            "comparison_json": sample["generated_json"],
            "type": "same",
        }
        comparison_samples.append(same_comparison)

    # Create different samples (generated_json from one sample vs generated_json from another)
    num_samples = len(filtered_data)
    for i in range(num_samples):
        # Select a random different sample
        j = random.choice([x for x in range(num_samples) if x != i])

        # Use the prompt from the first sample (i) for different comparisons
        original_index_i = original_indices[i]
        prompt = (
            prompts_data[original_index_i]["prompt"]
            if original_index_i < len(prompts_data)
            else f"Compare generated workflow from sample {i+1} with generated workflow from sample {j+1}"
        )

        different_comparison = {
            "prompt": prompt,
            "original_json": filtered_data[i]["ground_truth_json"],
            "comparison_json": filtered_data[j]["generated_json"],
            "type": "different",
        }
        comparison_samples.append(different_comparison)

    return comparison_samples


def main():
    # Input and output file paths
    input_file = "/home/<USER>/Internship/sample similarity/gemini sample generted.json"
    prompts_file = "/home/<USER>/Internship/sample similarity/sample_dataset.jsonl"
    output_file = "comparison_data.json"

    # Load prompts data from JSONL
    print("Loading prompts from JSONL file...")
    prompts_data = load_jsonl_data(prompts_file)
    print(f"Loaded {len(prompts_data)} prompts from JSONL file")

    # Load and filter data
    filtered_data, original_indices = load_and_filter_data(input_file)

    if not filtered_data:
        print("No valid samples found with generated_json. Exiting.")
        return

    # Create comparison samples
    comparison_samples = create_comparison_samples(
        filtered_data, prompts_data, original_indices
    )

    # Save the processed data
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(comparison_samples, f, indent=2, ensure_ascii=False)

    print(f"\nProcessed data saved to: {output_file}")
    print(f"Total comparison samples created: {len(comparison_samples)}")
    print(
        f"Same type samples: {len([s for s in comparison_samples if s['type'] == 'same'])}"
    )
    print(
        f"Different type samples: {len([s for s in comparison_samples if s['type'] == 'different'])}"
    )

    # Show a sample of the output structure
    print("\nSample output structure:")
    if comparison_samples:
        sample_output = comparison_samples[0].copy()
        # Truncate long JSON strings for display
        if len(sample_output["original_json"]) > 100:
            sample_output["original_json"] = (
                sample_output["original_json"][:100] + "..."
            )
        if len(sample_output["comparison_json"]) > 100:
            sample_output["comparison_json"] = (
                sample_output["comparison_json"][:100] + "..."
            )

        print(json.dumps(sample_output, indent=2))


if __name__ == "__main__":
    main()
